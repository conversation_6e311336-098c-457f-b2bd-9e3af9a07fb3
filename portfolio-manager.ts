import { RestClientV5 } from "bybit-api";
import { AnalyticsEngine, TradingSignal } from "./analytics-engine.ts";
import { RateLimiter } from "./rate-limiter.ts";
import { tradingConfig } from "./env.ts";

export interface Position {
  symbol: string;
  side: 'Buy' | 'Sell';
  size: number;
  entryPrice: number;
  markPrice: number;
  unrealizedPnl: number;
  percentage: number;
  leverage: number;
  timestamp: number;
}

export interface PortfolioState {
  totalBalance: number;
  availableBalance: number;
  unrealizedPnl: number;
  positions: Position[];
  positionCount: number;
  longPositions: number;
  shortPositions: number;
  netExposure: number;
  leverage: number;
  timestamp: number;
}

export interface RebalanceAction {
  symbol: string;
  action: 'OPEN_LONG' | 'OPEN_SHORT' | 'CLOSE' | 'ADJUST';
  currentSize: number;
  targetSize: number;
  reason: string;
  priority: number;
  expectedReturn: number;
  confidence: number;
}

export interface RiskMetrics {
  portfolioVaR: number; // Value at Risk
  maxDrawdown: number;
  sharpeRatio: number;
  correlationRisk: number;
  concentrationRisk: number;
  leverageRisk: number;
  overallRiskScore: number; // 0-1
}

export class PortfolioManager {
  private client: RestClientV5;
  private analyticsEngine: AnalyticsEngine;
  private rateLimiter: RateLimiter;

  private readonly rebalanceInterval = tradingConfig.rebalanceInterval;
  private readonly maxPositions = tradingConfig.maxPositions;
  private readonly leverage = tradingConfig.leverage;
  private readonly riskPerPosition = tradingConfig.riskPerPosition;
  private readonly minPositionSize = tradingConfig.minPositionSize;
  private readonly marketNeutralThreshold = tradingConfig.marketNeutralThreshold;
  private readonly maxLeverage = tradingConfig.maxLeverage;
  private readonly portfolioUtilization = tradingConfig.portfolioUtilization;

  private isRunning = false;
  private currentPortfolio: PortfolioState | null = null;

  constructor(client: RestClientV5, analyticsEngine: AnalyticsEngine, rateLimiter: RateLimiter) {
    this.client = client;
    this.analyticsEngine = analyticsEngine;
    this.rateLimiter = rateLimiter;
  }

  async initialize(): Promise<void> {
    console.log("💼 Initializing PortfolioManager...");
    
    // Get current portfolio state
    await this.updatePortfolioState();
    
    console.log("✅ PortfolioManager initialized");
  }

  async updatePortfolioState(): Promise<void> {
    try {
      await this.rateLimiter.waitIfNeeded();
      
      // Get wallet balance
      const balanceResponse = await this.client.getWalletBalance({
        accountType: 'UNIFIED'
      });
      
      if (!balanceResponse.result?.list?.[0]) {
        throw new Error("Unable to fetch wallet balance");
      }
      
      const account = balanceResponse.result.list[0];
      const usdtCoin = account.coin?.find(c => c.coin === 'USDT');
      
      if (!usdtCoin) {
        throw new Error("USDT balance not found");
      }
      
      // Get current positions
      await this.rateLimiter.waitIfNeeded();
      const positionsResponse = await this.client.getPositionInfo({
        category: 'linear'
      });
      
      const positions: Position[] = [];
      let totalUnrealizedPnl = 0;
      let longPositions = 0;
      let shortPositions = 0;
      let netExposure = 0;
      
      if (positionsResponse.result?.list) {
        for (const pos of positionsResponse.result.list) {
          if (parseFloat(pos.size) > 0) {
            const position: Position = {
              symbol: pos.symbol,
              side: pos.side as 'Buy' | 'Sell',
              size: parseFloat(pos.size),
              entryPrice: parseFloat(pos.avgPrice),
              markPrice: parseFloat(pos.markPrice),
              unrealizedPnl: parseFloat(pos.unrealisedPnl),
              percentage: parseFloat((pos as any).percentage || '0'),
              leverage: parseFloat(pos.leverage || '1'),
              timestamp: Date.now()
            };
            
            positions.push(position);
            totalUnrealizedPnl += position.unrealizedPnl;
            
            const positionValue = position.size * position.markPrice;
            if (position.side === 'Buy') {
              longPositions++;
              netExposure += positionValue;
            } else {
              shortPositions++;
              netExposure -= positionValue;
            }
          }
        }
      }
      
      this.currentPortfolio = {
        totalBalance: parseFloat(usdtCoin.walletBalance),
        availableBalance: parseFloat(usdtCoin.availableToWithdraw),
        unrealizedPnl: totalUnrealizedPnl,
        positions,
        positionCount: positions.length,
        longPositions,
        shortPositions,
        netExposure,
        leverage: parseFloat(account.accountLTV || '0'),
        timestamp: Date.now()
      };
      
      console.log(`💼 Portfolio updated: ${positions.length} positions, $${this.currentPortfolio.totalBalance.toFixed(2)} total balance`);
      
    } catch (error) {
      console.error("❌ Error updating portfolio state:", error);
      throw error;
    }
  }

  async startRebalancing(): Promise<void> {
    if (this.isRunning) {
      console.log("⚠️ Rebalancing already running");
      return;
    }
    
    this.isRunning = true;
    console.log("⚖️ Starting automated portfolio rebalancing...");
    
    while (this.isRunning) {
      try {
        await this.performRebalance();
        console.log(`⏰ Next rebalance in ${this.rebalanceInterval / 1000 / 60} minutes`);
        await new Promise(resolve => setTimeout(resolve, this.rebalanceInterval));
      } catch (error) {
        console.error("❌ Error during rebalancing:", error);
        await new Promise(resolve => setTimeout(resolve, 300000)); // Wait 5 minutes on error
      }
    }
  }

  async performRebalance(): Promise<void> {
    console.log("⚖️ Performing portfolio rebalance...");
    
    // Update current portfolio state
    await this.updatePortfolioState();
    
    if (!this.currentPortfolio) {
      throw new Error("Portfolio state not available");
    }
    
    // Get latest trading signals
    const signals = await this.analyticsEngine.generateTradingSignals();
    
    // Calculate risk metrics
    const riskMetrics = await this.calculateRiskMetrics();
    
    // Generate rebalance actions
    const actions = await this.generateRebalanceActions(signals, riskMetrics);
    
    // Execute actions
    if (actions.length > 0) {
      console.log(`📋 Executing ${actions.length} rebalance actions...`);
      await this.executeRebalanceActions(actions);
    } else {
      console.log("✅ Portfolio is already optimally balanced");
    }
    
    // Update portfolio state after rebalancing
    await this.updatePortfolioState();
    
    console.log("✅ Rebalance completed");
  }

  private async calculateRiskMetrics(): Promise<RiskMetrics> {
    if (!this.currentPortfolio) {
      throw new Error("Portfolio state not available");
    }
    
    const positions = this.currentPortfolio.positions;
    const totalValue = this.currentPortfolio.totalBalance;
    
    // Calculate concentration risk
    const positionValues = positions.map(p => p.size * p.markPrice);
    const maxPosition = Math.max(...positionValues, 0);
    const concentrationRisk = totalValue > 0 ? maxPosition / totalValue : 0;
    
    // Calculate correlation risk (simplified)
    const correlationRisk = positions.length > 1 ? 0.5 : 0; // Placeholder
    
    // Calculate leverage risk
    const leverageRisk = Math.min(1, this.currentPortfolio.leverage / this.maxLeverage);
    
    // Overall risk score
    const overallRiskScore = (concentrationRisk * 0.4) + (correlationRisk * 0.3) + (leverageRisk * 0.3);
    
    return {
      portfolioVaR: 0, // Placeholder
      maxDrawdown: 0, // Placeholder
      sharpeRatio: 0, // Placeholder
      correlationRisk,
      concentrationRisk,
      leverageRisk,
      overallRiskScore
    };
  }

  private async generateRebalanceActions(signals: TradingSignal[], riskMetrics: RiskMetrics): Promise<RebalanceAction[]> {
    if (!this.currentPortfolio) return [];

    const actions: RebalanceAction[] = [];
    const currentPositions = new Map(this.currentPortfolio.positions.map(p => [p.symbol, p]));
    const availableBalance = this.currentPortfolio.availableBalance;

    // Filter signals based on risk and confidence
    const validSignals = signals.filter(signal =>
      signal.confidence > tradingConfig.minConfidenceThreshold &&
      signal.strength > tradingConfig.minSignalStrength &&
      riskMetrics.overallRiskScore < 0.8
    );

    // Separate long and short signals for market-neutral approach
    const longSignals = validSignals.filter(s => s.action === 'BUY').slice(0, tradingConfig.maxPositionsPerSide);
    const shortSignals = validSignals.filter(s => s.action === 'SELL').slice(0, tradingConfig.maxPositionsPerSide);

    // Calculate target exposure for market neutrality
    const totalTargetExposure = availableBalance * this.portfolioUtilization;
    const longTargetExposure = totalTargetExposure / 2;
    const shortTargetExposure = totalTargetExposure / 2;

    // Generate actions for long positions
    await this.generatePositionActions(longSignals, currentPositions, longTargetExposure, 'LONG', actions);

    // Generate actions for short positions
    await this.generatePositionActions(shortSignals, currentPositions, shortTargetExposure, 'SHORT', actions);

    // Check for positions to close (no corresponding signal or low confidence)
    for (const [symbol, position] of currentPositions) {
      const correspondingSignal = validSignals.find(s => s.symbol === symbol);

      if (!correspondingSignal || correspondingSignal.confidence < 0.4) {
        actions.push({
          symbol,
          action: 'CLOSE',
          currentSize: position.size * position.markPrice,
          targetSize: 0,
          reason: correspondingSignal ? 'Low confidence signal' : 'No supporting signal',
          priority: 0.9, // High priority for closing
          expectedReturn: 0,
          confidence: 1
        });
      }
    }

    // Ensure market neutrality by balancing long/short exposure
    const neutralityActions = await this.ensureMarketNeutrality(currentPositions, actions);
    actions.push(...neutralityActions);

    // Sort by priority
    actions.sort((a, b) => b.priority - a.priority);

    return actions;
  }

  private async generatePositionActions(
    signals: TradingSignal[],
    currentPositions: Map<string, Position>,
    targetExposure: number,
    side: 'LONG' | 'SHORT',
    actions: RebalanceAction[]
  ): Promise<void> {
    if (signals.length === 0) return;

    // Calculate position size per signal based on weights and confidence
    const totalWeight = signals.reduce((sum, s) => sum + (s.strength * s.confidence), 0);

    for (const signal of signals) {
      const currentPosition = currentPositions.get(signal.symbol);
      const positionWeight = (signal.strength * signal.confidence) / totalWeight;
      const targetSize = targetExposure * positionWeight;

      if (!currentPosition) {
        // New position
        if (targetSize >= this.minPositionSize) {
          actions.push({
            symbol: signal.symbol,
            action: side === 'LONG' ? 'OPEN_LONG' : 'OPEN_SHORT',
            currentSize: 0,
            targetSize,
            reason: `New ${side} position with ${(signal.confidence * 100).toFixed(1)}% confidence`,
            priority: signal.strength * signal.confidence,
            expectedReturn: signal.expectedReturn,
            confidence: signal.confidence
          });
        }
      } else {
        // Existing position - check if adjustment needed
        const currentValue = currentPosition.size * currentPosition.markPrice;
        const sizeDifference = Math.abs(targetSize - currentValue);

        if (sizeDifference > targetSize * 0.15) { // 15% threshold for adjustment
          actions.push({
            symbol: signal.symbol,
            action: 'ADJUST',
            currentSize: currentValue,
            targetSize,
            reason: `Adjust ${side} position for market neutrality`,
            priority: signal.strength * signal.confidence * 0.8,
            expectedReturn: signal.expectedReturn,
            confidence: signal.confidence
          });
        }
      }
    }
  }

  private async ensureMarketNeutrality(
    currentPositions: Map<string, Position>,
    plannedActions: RebalanceAction[]
  ): Promise<RebalanceAction[]> {
    const neutralityActions: RebalanceAction[] = [];

    // Calculate current and planned exposures
    let currentLongExposure = 0;
    let currentShortExposure = 0;

    for (const position of currentPositions.values()) {
      const exposure = position.size * position.markPrice;
      if (position.side === 'Buy') {
        currentLongExposure += exposure;
      } else {
        currentShortExposure += exposure;
      }
    }

    // Calculate planned exposure changes
    let plannedLongChange = 0;
    let plannedShortChange = 0;

    for (const action of plannedActions) {
      if (action.action === 'OPEN_LONG' || (action.action === 'ADJUST' && action.targetSize > action.currentSize)) {
        plannedLongChange += action.targetSize - action.currentSize;
      } else if (action.action === 'OPEN_SHORT' || (action.action === 'ADJUST' && action.targetSize > action.currentSize)) {
        plannedShortChange += action.targetSize - action.currentSize;
      }
    }

    const projectedLongExposure = currentLongExposure + plannedLongChange;
    const projectedShortExposure = currentShortExposure + plannedShortChange;
    const exposureDifference = Math.abs(projectedLongExposure - projectedShortExposure);
    const totalExposure = projectedLongExposure + projectedShortExposure;

    // If imbalance exceeds threshold, add balancing actions
    if (totalExposure > 0 && exposureDifference / totalExposure > this.marketNeutralThreshold) {
      console.log(`⚖️ Market neutrality adjustment needed. Long: $${projectedLongExposure.toFixed(2)}, Short: $${projectedShortExposure.toFixed(2)}`);

      // Add logic to balance exposure if needed
      // This could involve reducing positions on the over-exposed side
      // or increasing positions on the under-exposed side
    }

    return neutralityActions;
  }

  private calculateTargetPositionSize(signal: TradingSignal, maxPositionValue: number): number {
    // Calculate position size based on signal strength and confidence
    const baseSize = maxPositionValue * signal.strength * signal.confidence;

    // Adjust for expected return (higher expected return = larger position)
    const returnAdjustment = Math.min(2, Math.max(0.5, 1 + signal.expectedReturn * 10));

    return Math.max(this.minPositionSize, baseSize * returnAdjustment);
  }

  private async executeRebalanceActions(actions: RebalanceAction[]): Promise<void> {
    for (const action of actions) {
      try {
        await this.executeAction(action);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay between actions
      } catch (error) {
        console.error(`❌ Error executing action for ${action.symbol}:`, error);
      }
    }
  }

  private async executeAction(action: RebalanceAction): Promise<void> {
    console.log(`🔄 ${action.action} ${action.symbol}: ${action.reason}`);
    
    // In demo mode, just log the action
    if (Deno.env.get('DEMO_MODE') === 'true') {
      console.log(`📝 DEMO: Would execute ${action.action} for ${action.symbol}`);
      return;
    }
    
    await this.rateLimiter.waitIfNeeded();
    
    switch (action.action) {
      case 'OPEN_LONG':
        await this.openPosition(action.symbol, 'Buy', action.targetSize);
        break;
      case 'OPEN_SHORT':
        await this.openPosition(action.symbol, 'Sell', action.targetSize);
        break;
      case 'CLOSE':
        await this.closePosition(action.symbol);
        break;
      case 'ADJUST':
        await this.adjustPosition(action.symbol, action.targetSize);
        break;
    }
  }

  private async openPosition(symbol: string, side: 'Buy' | 'Sell', value: number): Promise<void> {
    try {
      // Get current price to calculate quantity
      await this.rateLimiter.waitIfNeeded();
      const tickerResponse = await this.client.getTickers({
        category: 'linear',
        symbol: symbol
      });
      
      if (!tickerResponse.result?.list?.[0]) {
        throw new Error(`Unable to get price for ${symbol}`);
      }
      
      const currentPrice = parseFloat(tickerResponse.result.list[0].lastPrice);
      const quantity = (value / currentPrice).toFixed(3);
      
      console.log(`📈 Opening ${side} position: ${quantity} ${symbol} at $${currentPrice}`);
      
      // Set leverage to 2x before placing order
      await this.rateLimiter.waitIfNeeded();
      await this.client.setLeverage({
        category: 'linear',
        symbol: symbol,
        buyLeverage: this.leverage.toString(),
        sellLeverage: this.leverage.toString()
      });

      // Place market order with 2x leverage
      await this.rateLimiter.waitIfNeeded();
      const orderResponse = await this.client.submitOrder({
        category: 'linear',
        symbol: symbol,
        side: side,
        orderType: 'Market',
        qty: quantity,
        timeInForce: 'IOC'
      });
      
      if (orderResponse.result?.orderId) {
        console.log(`✅ Order placed: ${orderResponse.result.orderId}`);
      }
      
    } catch (error) {
      console.error(`❌ Error opening position for ${symbol}:`, error);
      throw error;
    }
  }

  private async closePosition(symbol: string): Promise<void> {
    try {
      console.log(`🔒 Closing position: ${symbol}`);
      
      await this.rateLimiter.waitIfNeeded();
      const closeResponse = await this.client.submitOrder({
        category: 'linear',
        symbol: symbol,
        side: 'Buy', // Will be automatically determined by the API
        orderType: 'Market',
        qty: '0', // Close entire position
        reduceOnly: true,
        timeInForce: 'IOC'
      });
      
      if (closeResponse.result?.orderId) {
        console.log(`✅ Position closed: ${closeResponse.result.orderId}`);
      }
      
    } catch (error) {
      console.error(`❌ Error closing position for ${symbol}:`, error);
      throw error;
    }
  }

  private async adjustPosition(symbol: string, targetValue: number): Promise<void> {
    // For simplicity, close and reopen position
    await this.closePosition(symbol);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    
    // Determine side based on current signals
    const signals = await this.analyticsEngine.generateTradingSignals();
    const signal = signals.find(s => s.symbol === symbol);
    
    if (signal) {
      const side = signal.action === 'BUY' ? 'Buy' : 'Sell';
      await this.openPosition(symbol, side, targetValue);
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    console.log("⏹️ Portfolio manager stopped");
  }

  getCurrentPortfolio(): PortfolioState | null {
    return this.currentPortfolio;
  }

  async getPortfolioSummary(): Promise<any> {
    if (!this.currentPortfolio) {
      await this.updatePortfolioState();
    }
    
    const riskMetrics = await this.calculateRiskMetrics();
    
    return {
      portfolio: this.currentPortfolio,
      riskMetrics,
      isRebalancing: this.isRunning,
      lastUpdate: this.currentPortfolio?.timestamp || 0
    };
  }
}
