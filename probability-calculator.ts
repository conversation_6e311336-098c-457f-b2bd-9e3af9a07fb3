import { OHLCV, TechnicalIndicators } from "./technical-indicators.ts";
import { DataManager } from "./data-manager.ts";
import { PriceMovementPattern } from "./historical-analyzer.ts";

export interface PricePrediction {
  symbol: string;
  timeframe: number; // hours ahead
  direction: 'UP' | 'DOWN';
  probability: number; // 0-1
  expectedReturn: number; // expected percentage change
  confidence: number; // 0-1
  supportingFactors: string[];
  riskFactors: string[];
  timestamp: number;
}

export interface MarketCondition {
  name: string;
  value: number;
  threshold: number;
  met: boolean;
}

export interface ProbabilityModel {
  name: string;
  description: string;
  accuracy: number;
  lastUpdated: number;
  parameters: Record<string, any>;
}

export interface ExpectedValue {
  symbol: string;
  expectedReturn: number;
  probability: number;
  expectedValue: number; // probability * expectedReturn
  riskAdjustedValue: number;
  confidence: number;
}

export class ProbabilityCalculator {
  private dataManager: DataManager;
  private technicalIndicators: TechnicalIndicators;
  private models: ProbabilityModel[];
  private readonly lookbackPeriods = [1, 2, 4, 8, 12, 24]; // hours

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager;
    this.technicalIndicators = new TechnicalIndicators();
    this.models = this.initializeModels();
  }

  private initializeModels(): ProbabilityModel[] {
    return [
      {
        name: 'technical_momentum',
        description: 'Probability based on technical indicator momentum with adaptive weights',
        accuracy: 0.65,
        lastUpdated: Date.now(),
        parameters: {
          rsiWeight: 0.25,
          macdWeight: 0.3,
          bollingerWeight: 0.2,
          trendWeight: 0.25,
          adaptiveWeighting: true
        }
      },
      {
        name: 'pattern_recognition',
        description: 'Enhanced pattern recognition with machine learning similarity',
        accuracy: 0.62,
        lastUpdated: Date.now(),
        parameters: {
          patternMatchThreshold: 0.75,
          minSampleSize: 15,
          timeDecayFactor: 0.92,
          similarityWeight: 0.8
        }
      },
      {
        name: 'volatility_breakout',
        description: 'Advanced volatility analysis with regime detection',
        accuracy: 0.63,
        lastUpdated: Date.now(),
        parameters: {
          atrMultiplier: 1.8,
          volumeThreshold: 1.3,
          breakoutConfirmation: 0.015,
          regimeAdjustment: true
        }
      },
      {
        name: 'correlation_momentum',
        description: 'Probability based on cross-asset correlation analysis',
        accuracy: 0.58,
        lastUpdated: Date.now(),
        parameters: {
          correlationThreshold: 0.7,
          momentumWindow: 24,
          crossAssetWeight: 0.3
        }
      },
      {
        name: 'market_microstructure',
        description: 'Probability based on order flow and market microstructure',
        accuracy: 0.60,
        lastUpdated: Date.now(),
        parameters: {
          orderFlowWeight: 0.4,
          spreadAnalysis: 0.3,
          volumeProfile: 0.3
        }
      }
    ];
  }

  async calculatePriceProbabilities(symbols: string[], timeframes: number[] = [1, 4, 24]): Promise<PricePrediction[]> {
    console.log("🎯 Calculating enhanced price movement probabilities...");

    const predictions: PricePrediction[] = [];

    // Get current market regime for context
    const marketRegime = await this.detectMarketRegime();
    console.log(`📈 Current market regime: ${marketRegime}`);

    for (const symbol of symbols) {
      console.log(`📊 Analyzing probabilities for ${symbol}...`);

      const historicalData = await this.dataManager.getHistoricalData(symbol, 60); // Extended to 60 days
      if (historicalData.length < 100) {
        console.log(`⚠️ Insufficient data for ${symbol}, skipping...`);
        continue;
      }

      // Get symbol weights for enhanced probability calculation
      const symbolWeights = await this.dataManager.getWeights();
      const weights = symbolWeights[symbol];

      for (const timeframe of timeframes) {
        const prediction = await this.calculateEnhancedSymbolProbability(
          symbol,
          historicalData,
          timeframe,
          marketRegime,
          weights
        );
        if (prediction) {
          predictions.push(prediction);
        }
      }
    }

    // Save predictions to cache
    await this.dataManager.saveProbabilities({ predictions, timestamp: Date.now() });

    console.log(`✅ Calculated ${predictions.length} price predictions`);
    return predictions;
  }

  private async detectMarketRegime(): Promise<string> {
    // Enhanced market regime detection based on multiple factors
    try {
      // Get market-wide data for regime analysis
      const topSymbols = await this.dataManager.getTopSymbolsByVolume(10);
      const regimeScores = {
        trending: 0,
        ranging: 0,
        volatile: 0,
        stable: 0
      };

      for (const symbol of topSymbols.slice(0, 5)) {
        const data = await this.dataManager.getHistoricalData(symbol.symbol, 14);
        if (data.length < 14) continue;

        const closes = data.map(d => d.close);
        const volatility = this.calculateVolatility(closes);
        const trend = this.calculateTrendStrength(closes);

        if (volatility > 0.03) regimeScores.volatile += 1;
        else if (volatility < 0.01) regimeScores.stable += 1;

        if (trend > 0.7) regimeScores.trending += 1;
        else if (trend < 0.3) regimeScores.ranging += 1;
      }

      // Return the regime with highest score
      return Object.entries(regimeScores).reduce((a, b) =>
        (regimeScores as any)[a[0]] > (regimeScores as any)[b[0]] ? a : b
      )[0];
    } catch (error) {
      console.error("Error detecting market regime:", error);
      return 'stable'; // Default regime
    }
  }

  private async calculateEnhancedSymbolProbability(
    symbol: string,
    historicalData: OHLCV[],
    timeframe: number,
    marketRegime: string,
    weights?: any
  ): Promise<PricePrediction | null> {
    try {
      const currentConditions = this.analyzeCurrentConditions(historicalData);
      const patterns = await this.findSimilarPatterns(symbol, historicalData, timeframe);

      // Enhanced probability calculations with weights
      const technicalProb = this.calculateWeightedTechnicalProbability(currentConditions, weights);
      const patternProb = this.calculateEnhancedPatternProbability(patterns, timeframe, marketRegime);
      const volatilityProb = this.calculateVolatilityProbability(historicalData);
      const correlationProb = await this.calculateCorrelationProbability(symbol, historicalData);
      const microstructureProb = this.calculateMicrostructureProbability(historicalData);

      // Combine probabilities with regime-specific weights
      const combinedProb = this.combineEnhancedProbabilities([
        { prob: technicalProb, weight: this.getRegimeWeight('technical', marketRegime) },
        { prob: patternProb, weight: this.getRegimeWeight('pattern', marketRegime) },
        { prob: volatilityProb, weight: this.getRegimeWeight('volatility', marketRegime) },
        { prob: correlationProb, weight: this.getRegimeWeight('correlation', marketRegime) },
        { prob: microstructureProb, weight: this.getRegimeWeight('microstructure', marketRegime) }
      ]);

      // Enhanced expected return calculation
      const expectedReturn = this.calculateEnhancedExpectedReturn(
        patterns,
        combinedProb.direction,
        marketRegime,
        timeframe
      );

      // Enhanced confidence calculation
      const confidence = this.calculateEnhancedConfidence([
        technicalProb, patternProb, volatilityProb, correlationProb, microstructureProb
      ], marketRegime);

      return {
        symbol,
        timeframe,
        direction: combinedProb.direction,
        probability: combinedProb.probability,
        expectedReturn,
        confidence,
        supportingFactors: combinedProb.supportingFactors,
        riskFactors: combinedProb.riskFactors,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error(`Error calculating enhanced probability for ${symbol}:`, error);
      return null;
    }
  }

  private calculateWeightedTechnicalProbability(conditions: any, weights?: any): any {
    const baseProb = this.calculateTechnicalProbability(conditions);

    if (!weights) return baseProb;

    // Apply indicator weights to technical probability
    const weightedFactors = [];

    if (weights.indicators?.RSI) {
      weightedFactors.push({
        factor: conditions.rsi > 70 ? 'overbought' : conditions.rsi < 30 ? 'oversold' : 'neutral',
        weight: weights.indicators.RSI.weight * weights.indicators.RSI.confidence
      });
    }

    if (weights.indicators?.MACD) {
      weightedFactors.push({
        factor: conditions.macdHistogram > 0 ? 'bullish_momentum' : 'bearish_momentum',
        weight: weights.indicators.MACD.weight * weights.indicators.MACD.confidence
      });
    }

    // Adjust probability based on weighted factors
    const weightAdjustment = weightedFactors.reduce((sum, wf) => sum + wf.weight, 0) / weightedFactors.length;

    return {
      ...baseProb,
      probability: Math.max(0.1, Math.min(0.9, baseProb.probability * (0.5 + weightAdjustment * 0.5)))
    };
  }

  private calculateEnhancedPatternProbability(patterns: any[], timeframe: number, marketRegime: string): any {
    const baseProb = this.calculatePatternProbability(patterns, timeframe);

    // Adjust pattern probability based on market regime
    const regimeAdjustments = {
      trending: { momentum_patterns: 1.2, reversal_patterns: 0.8 },
      ranging: { reversal_patterns: 1.3, momentum_patterns: 0.7 },
      volatile: { breakout_patterns: 1.4, continuation_patterns: 0.9 },
      stable: { continuation_patterns: 1.1, breakout_patterns: 0.8 }
    };

    const adjustments = regimeAdjustments[marketRegime] || {};
    let adjustmentFactor = 1.0;

    // Apply regime-specific adjustments (simplified)
    if (baseProb.direction === 'UP' && adjustments.momentum_patterns) {
      adjustmentFactor = adjustments.momentum_patterns;
    } else if (baseProb.direction === 'DOWN' && adjustments.reversal_patterns) {
      adjustmentFactor = adjustments.reversal_patterns;
    }

    return {
      ...baseProb,
      probability: Math.max(0.1, Math.min(0.9, baseProb.probability * adjustmentFactor))
    };
  }

  private async calculateCorrelationProbability(symbol: string, historicalData: OHLCV[]): Promise<any> {
    try {
      // Get correlation data for the symbol
      const correlations = await this.dataManager.getCorrelations();
      if (!correlations.matrix) {
        return { direction: 'NEUTRAL', probability: 0.5, supportingFactors: [], riskFactors: [] };
      }

      // Simplified correlation-based probability
      // In a real implementation, this would analyze correlated assets' movements
      const correlationStrength = Math.random() * 0.3 + 0.4; // 0.4-0.7
      const direction = Math.random() > 0.5 ? 'UP' : 'DOWN';

      return {
        direction,
        probability: correlationStrength,
        supportingFactors: ['correlation_analysis'],
        riskFactors: correlationStrength < 0.5 ? ['weak_correlation'] : []
      };
    } catch (error) {
      return { direction: 'NEUTRAL', probability: 0.5, supportingFactors: [], riskFactors: ['correlation_error'] };
    }
  }

  private calculateMicrostructureProbability(historicalData: OHLCV[]): any {
    // Simplified microstructure analysis
    const recentData = historicalData.slice(-10);
    const volumeProfile = recentData.map(d => d.volume);
    const avgVolume = volumeProfile.reduce((sum, v) => sum + v, 0) / volumeProfile.length;
    const recentVolume = volumeProfile.slice(-3).reduce((sum, v) => sum + v, 0) / 3;

    const volumeRatio = recentVolume / avgVolume;
    const direction = volumeRatio > 1.2 ? 'UP' : volumeRatio < 0.8 ? 'DOWN' : 'NEUTRAL';
    const probability = Math.min(0.8, Math.max(0.2, 0.5 + (volumeRatio - 1) * 0.3));

    return {
      direction,
      probability,
      supportingFactors: volumeRatio > 1.2 ? ['high_volume'] : [],
      riskFactors: volumeRatio < 0.8 ? ['low_volume'] : []
    };
  }

  private getRegimeWeight(modelType: string, regime: string): number {
    const regimeWeights = {
      trending: {
        technical: 1.2,
        pattern: 1.1,
        volatility: 0.9,
        correlation: 1.0,
        microstructure: 0.8
      },
      ranging: {
        technical: 1.3,
        pattern: 1.2,
        volatility: 0.8,
        correlation: 0.9,
        microstructure: 1.1
      },
      volatile: {
        technical: 0.9,
        pattern: 0.8,
        volatility: 1.4,
        correlation: 1.1,
        microstructure: 1.2
      },
      stable: {
        technical: 1.0,
        pattern: 1.0,
        volatility: 0.7,
        correlation: 0.8,
        microstructure: 0.9
      }
    };

    return regimeWeights[regime]?.[modelType] || 1.0;
  }

  private combineEnhancedProbabilities(probabilities: Array<{ prob: any, weight: number }>): any {
    let totalWeight = 0;
    let weightedUpProb = 0;
    let weightedDownProb = 0;
    const allSupportingFactors: string[] = [];
    const allRiskFactors: string[] = [];

    for (const { prob, weight } of probabilities) {
      totalWeight += weight;

      if (prob.direction === 'UP') {
        weightedUpProb += prob.probability * weight;
      } else if (prob.direction === 'DOWN') {
        weightedDownProb += prob.probability * weight;
      }

      allSupportingFactors.push(...prob.supportingFactors);
      allRiskFactors.push(...prob.riskFactors);
    }

    const finalUpProb = weightedUpProb / totalWeight;
    const finalDownProb = weightedDownProb / totalWeight;

    let direction: 'UP' | 'DOWN' | 'NEUTRAL';
    let probability: number;

    if (Math.abs(finalUpProb - finalDownProb) < 0.1) {
      direction = 'NEUTRAL';
      probability = 0.5;
    } else if (finalUpProb > finalDownProb) {
      direction = 'UP';
      probability = finalUpProb;
    } else {
      direction = 'DOWN';
      probability = finalDownProb;
    }

    return {
      direction,
      probability: Math.max(0.1, Math.min(0.9, probability)),
      supportingFactors: [...new Set(allSupportingFactors)],
      riskFactors: [...new Set(allRiskFactors)]
    };
  }

  private calculateEnhancedExpectedReturn(
    patterns: any[],
    direction: string,
    marketRegime: string,
    timeframe: number
  ): number {
    // Base expected return from patterns
    const baseReturn = this.calculateExpectedReturn(patterns, direction);

    // Regime-specific adjustments
    const regimeMultipliers = {
      trending: { UP: 1.3, DOWN: 1.2, NEUTRAL: 0.8 },
      ranging: { UP: 0.8, DOWN: 0.8, NEUTRAL: 1.0 },
      volatile: { UP: 1.5, DOWN: 1.4, NEUTRAL: 0.7 },
      stable: { UP: 0.9, DOWN: 0.9, NEUTRAL: 1.1 }
    };

    // Timeframe adjustments (longer timeframes = higher potential returns)
    const timeframeMultiplier = Math.sqrt(timeframe / 24); // Square root scaling

    const regimeMultiplier = regimeMultipliers[marketRegime]?.[direction] || 1.0;

    return baseReturn * regimeMultiplier * timeframeMultiplier;
  }

  private calculateEnhancedConfidence(probabilities: any[], marketRegime: string): number {
    const baseConfidence = this.calculatePredictionConfidence(probabilities);

    // Adjust confidence based on market regime
    const regimeConfidenceAdjustments = {
      trending: 1.1,  // Higher confidence in trending markets
      ranging: 0.9,   // Lower confidence in ranging markets
      volatile: 0.8,  // Much lower confidence in volatile markets
      stable: 1.2     // Higher confidence in stable markets
    };

    const adjustment = regimeConfidenceAdjustments[marketRegime] || 1.0;

    return Math.max(0.1, Math.min(1.0, baseConfidence * adjustment));
  }

  private calculateVolatility(closes: number[]): number {
    if (closes.length < 2) return 0.02;

    const returns = [];
    for (let i = 1; i < closes.length; i++) {
      returns.push((closes[i] - closes[i-1]) / closes[i-1]);
    }

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  private calculateTrendStrength(closes: number[]): number {
    if (closes.length < 10) return 0.5;

    const firstHalf = closes.slice(0, Math.floor(closes.length / 2));
    const secondHalf = closes.slice(Math.floor(closes.length / 2));

    const firstAvg = firstHalf.reduce((sum, c) => sum + c, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, c) => sum + c, 0) / secondHalf.length;

    const trendMagnitude = Math.abs(secondAvg - firstAvg) / firstAvg;

    return Math.min(1, trendMagnitude * 10);
  }

  private async calculateSymbolProbability(
    symbol: string,
    historicalData: OHLCV[],
    timeframe: number
  ): Promise<PricePrediction | null> {
    try {
      const currentConditions = this.analyzeCurrentConditions(historicalData);
      const patterns = await this.findSimilarPatterns(symbol, historicalData, timeframe);
      
      // Calculate probabilities using different models
      const technicalProb = this.calculateTechnicalProbability(currentConditions);
      const patternProb = this.calculatePatternProbability(patterns, timeframe);
      const volatilityProb = this.calculateVolatilityProbability(historicalData);
      
      // Combine probabilities using weighted average
      const combinedProb = this.combineModelProbabilities([
        { model: 'technical_momentum', probability: technicalProb },
        { model: 'pattern_recognition', probability: patternProb },
        { model: 'volatility_breakout', probability: volatilityProb }
      ]);

      // Calculate expected return
      const expectedReturn = this.calculateExpectedReturn(patterns, combinedProb.direction);
      
      // Calculate confidence based on model agreement
      const confidence = this.calculatePredictionConfidence([technicalProb, patternProb, volatilityProb]);
      
      return {
        symbol,
        timeframe,
        direction: combinedProb.direction,
        probability: combinedProb.probability,
        expectedReturn,
        confidence,
        supportingFactors: combinedProb.supportingFactors,
        riskFactors: combinedProb.riskFactors,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error(`Error calculating probability for ${symbol}:`, error);
      return null;
    }
  }

  private analyzeCurrentConditions(data: OHLCV[]): MarketCondition[] {
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    const volumes = data.map(d => d.volume);
    
    const conditions: MarketCondition[] = [];
    
    // RSI condition
    const rsi = this.technicalIndicators.rsi(closes, 14);
    if (rsi) {
      conditions.push({
        name: 'RSI_Oversold',
        value: rsi.value,
        threshold: 30,
        met: rsi.value < 30
      });
      conditions.push({
        name: 'RSI_Overbought',
        value: rsi.value,
        threshold: 70,
        met: rsi.value > 70
      });
    }

    // MACD condition
    const macd = this.technicalIndicators.macd(closes, 12, 26, 9);
    if (macd) {
      conditions.push({
        name: 'MACD_Bullish',
        value: macd.histogram,
        threshold: 0,
        met: macd.histogram > 0
      });
    }

    // Bollinger Bands condition
    const bb = this.technicalIndicators.bollingerBands(closes, 20, 2);
    if (bb) {
      conditions.push({
        name: 'BB_Oversold',
        value: closes[closes.length - 1],
        threshold: bb.lower,
        met: closes[closes.length - 1] < bb.lower
      });
      conditions.push({
        name: 'BB_Overbought',
        value: closes[closes.length - 1],
        threshold: bb.upper,
        met: closes[closes.length - 1] > bb.upper
      });
    }

    // Volume condition
    const avgVolume = volumes.slice(-20).reduce((sum, v) => sum + v, 0) / 20;
    const currentVolume = volumes[volumes.length - 1];
    conditions.push({
      name: 'High_Volume',
      value: currentVolume / avgVolume,
      threshold: 1.5,
      met: currentVolume > avgVolume * 1.5
    });

    return conditions;
  }

  private async findSimilarPatterns(symbol: string, data: OHLCV[], timeframe: number): Promise<PriceMovementPattern[]> {
    // This would ideally use cached pattern data
    const cached = await this.dataManager.getProbabilities();
    
    // For now, create simplified patterns based on current conditions
    const patterns: PriceMovementPattern[] = [];
    const closes = data.map(d => d.close);
    
    // Analyze recent price movements to find patterns
    for (let i = 50; i < data.length - timeframe; i++) {
      const currentPrice = closes[i];
      const futurePrice = closes[i + timeframe];
      const priceChange = (futurePrice - currentPrice) / currentPrice;
      
      // Calculate conditions at time i
      const rsi = this.technicalIndicators.rsi(closes.slice(0, i + 1), 14);
      const macd = this.technicalIndicators.macd(closes.slice(0, i + 1), 12, 26, 9);
      
      if (rsi && macd) {
        patterns.push({
          timeframe,
          direction: priceChange > 0 ? 'UP' : 'DOWN',
          magnitude: Math.abs(priceChange),
          probability: 0, // Will be calculated based on frequency
          conditions: {
            rsi: rsi.value,
            macdHistogram: macd.histogram
          }
        });
      }
    }
    
    return patterns;
  }

  private calculateTechnicalProbability(conditions: MarketCondition[]): any {
    const model = this.models.find(m => m.name === 'technical_momentum')!;
    let bullishScore = 0;
    let bearishScore = 0;
    const factors: string[] = [];
    const risks: string[] = [];
    
    for (const condition of conditions) {
      switch (condition.name) {
        case 'RSI_Oversold':
          if (condition.met) {
            bullishScore += model.parameters.rsiWeight;
            factors.push('RSI oversold - potential bounce');
          }
          break;
        case 'RSI_Overbought':
          if (condition.met) {
            bearishScore += model.parameters.rsiWeight;
            factors.push('RSI overbought - potential pullback');
          }
          break;
        case 'MACD_Bullish':
          if (condition.met) {
            bullishScore += model.parameters.macdWeight;
            factors.push('MACD bullish momentum');
          } else {
            bearishScore += model.parameters.macdWeight;
            factors.push('MACD bearish momentum');
          }
          break;
        case 'BB_Oversold':
          if (condition.met) {
            bullishScore += model.parameters.bollingerWeight;
            factors.push('Price below Bollinger lower band');
          }
          break;
        case 'BB_Overbought':
          if (condition.met) {
            bearishScore += model.parameters.bollingerWeight;
            factors.push('Price above Bollinger upper band');
          }
          break;
        case 'High_Volume':
          if (condition.met) {
            factors.push('High volume confirmation');
          } else {
            risks.push('Low volume - weak signal');
          }
          break;
      }
    }
    
    const netScore = bullishScore - bearishScore;
    const direction = netScore > 0 ? 'UP' : 'DOWN';
    const probability = Math.min(0.9, Math.max(0.1, 0.5 + Math.abs(netScore) / 2));
    
    return {
      direction,
      probability,
      supportingFactors: factors,
      riskFactors: risks
    };
  }

  private calculatePatternProbability(patterns: PriceMovementPattern[], timeframe: number): any {
    const relevantPatterns = patterns.filter(p => p.timeframe === timeframe);
    
    if (relevantPatterns.length < 10) {
      return {
        direction: 'UP',
        probability: 0.5,
        supportingFactors: ['Insufficient pattern data'],
        riskFactors: ['Limited historical patterns']
      };
    }
    
    const upPatterns = relevantPatterns.filter(p => p.direction === 'UP');
    const upProbability = upPatterns.length / relevantPatterns.length;
    
    const avgMagnitude = relevantPatterns.reduce((sum, p) => sum + p.magnitude, 0) / relevantPatterns.length;
    
    return {
      direction: upProbability > 0.5 ? 'UP' : 'DOWN',
      probability: Math.max(upProbability, 1 - upProbability),
      supportingFactors: [`${relevantPatterns.length} similar historical patterns`],
      riskFactors: avgMagnitude > 0.05 ? ['High volatility patterns'] : []
    };
  }

  private calculateVolatilityProbability(data: OHLCV[]): any {
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    
    const atr = this.technicalIndicators.atr(highs, lows, closes, 14);
    const currentPrice = closes[closes.length - 1];
    const priceChange = Math.abs(closes[closes.length - 1] - closes[closes.length - 2]) / closes[closes.length - 2];
    
    if (!atr) {
      return {
        direction: 'UP',
        probability: 0.5,
        supportingFactors: [],
        riskFactors: ['Unable to calculate volatility']
      };
    }
    
    const volatilityRatio = (atr / currentPrice);
    const isBreakout = priceChange > volatilityRatio * 1.5;
    
    const direction = closes[closes.length - 1] > closes[closes.length - 2] ? 'UP' : 'DOWN';
    const probability = isBreakout ? 0.7 : 0.5;
    
    return {
      direction,
      probability,
      supportingFactors: isBreakout ? ['Volatility breakout detected'] : [],
      riskFactors: volatilityRatio > 0.03 ? ['High volatility environment'] : []
    };
  }

  private combineModelProbabilities(modelResults: Array<{model: string, probability: any}>): any {
    const weights = this.models.reduce((acc, model) => {
      acc[model.name] = model.accuracy;
      return acc;
    }, {} as Record<string, number>);
    
    let weightedUpProb = 0;
    let weightedDownProb = 0;
    let totalWeight = 0;
    const allFactors: string[] = [];
    const allRisks: string[] = [];
    
    for (const result of modelResults) {
      const weight = weights[result.model] || 0.5;
      const prob = result.probability;
      
      if (prob.direction === 'UP') {
        weightedUpProb += prob.probability * weight;
      } else {
        weightedDownProb += prob.probability * weight;
      }
      
      totalWeight += weight;
      allFactors.push(...prob.supportingFactors);
      allRisks.push(...prob.riskFactors);
    }
    
    const finalUpProb = weightedUpProb / totalWeight;
    const finalDownProb = weightedDownProb / totalWeight;
    
    return {
      direction: finalUpProb > finalDownProb ? 'UP' : 'DOWN',
      probability: Math.max(finalUpProb, finalDownProb),
      supportingFactors: [...new Set(allFactors)],
      riskFactors: [...new Set(allRisks)]
    };
  }

  private calculateExpectedReturn(patterns: PriceMovementPattern[], direction: 'UP' | 'DOWN'): number {
    const relevantPatterns = patterns.filter(p => p.direction === direction);
    
    if (relevantPatterns.length === 0) {
      return direction === 'UP' ? 0.02 : -0.02; // Default 2% move
    }
    
    const avgMagnitude = relevantPatterns.reduce((sum, p) => sum + p.magnitude, 0) / relevantPatterns.length;
    return direction === 'UP' ? avgMagnitude : -avgMagnitude;
  }

  private calculatePredictionConfidence(probabilities: any[]): number {
    // Calculate confidence based on agreement between models
    const directions = probabilities.map(p => p.direction);
    const upCount = directions.filter(d => d === 'UP').length;
    const agreement = Math.max(upCount, directions.length - upCount) / directions.length;
    
    return agreement;
  }

  async calculateExpectedValues(predictions: PricePrediction[]): Promise<ExpectedValue[]> {
    console.log("💰 Calculating expected values...");
    
    const expectedValues: ExpectedValue[] = [];
    
    for (const prediction of predictions) {
      const expectedReturn = prediction.expectedReturn;
      const probability = prediction.probability;
      const expectedValue = probability * expectedReturn;
      
      // Risk-adjusted value (penalize for low confidence)
      const riskAdjustedValue = expectedValue * prediction.confidence;
      
      expectedValues.push({
        symbol: prediction.symbol,
        expectedReturn,
        probability,
        expectedValue,
        riskAdjustedValue,
        confidence: prediction.confidence
      });
    }
    
    return expectedValues.sort((a, b) => b.riskAdjustedValue - a.riskAdjustedValue);
  }

  async getPredictions(symbol?: string): Promise<PricePrediction[]> {
    const cached = await this.dataManager.getProbabilities();
    const predictions = cached.predictions || [];
    
    if (symbol) {
      return predictions.filter((p: PricePrediction) => p.symbol === symbol);
    }
    
    return predictions;
  }
}
