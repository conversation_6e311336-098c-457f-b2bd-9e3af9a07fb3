{"historical": {}, "market": {"symbols": [{"symbol": "1000000BABYDOGEUSDT", "baseCoin": "1000000BABYDOGE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000000CHEEMSUSDT", "baseCoin": "1000000CHEEMS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000000MOGUSDT", "baseCoin": "1000000MOG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000000PEIPEIUSDT", "baseCoin": "1000000PEIPEI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000COQUSDT", "baseCoin": "10000COQ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000ELONUSDT", "baseCoin": "10000ELON", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000LADYSUSDT", "baseCoin": "10000LADYS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000QUBICUSDT", "baseCoin": "10000QUBIC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000SATSUSDT", "baseCoin": "10000SATS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000WENUSDT", "baseCoin": "10000WEN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "10000WHYUSDT", "baseCoin": "10000WHY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000APUUSDT", "baseCoin": "1000APU", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000BONKUSDT", "baseCoin": "1000BONK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000BTTUSDT", "baseCoin": "1000BTT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000CATSUSDT", "baseCoin": "1000CATS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000CATUSDT", "baseCoin": "1000CAT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000FLOKIUSDT", "baseCoin": "1000FLOKI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000LUNCUSDT", "baseCoin": "1000LUNC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000NEIROCTOUSDT", "baseCoin": "1000NEIROCTO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000PEPEUSDT", "baseCoin": "1000PEPE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000RATSUSDT", "baseCoin": "1000RATS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000TOSHIUSDT", "baseCoin": "1000TOSHI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000TURBOUSDT", "baseCoin": "1000TURBO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000XECUSDT", "baseCoin": "1000XEC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1000XUSDT", "baseCoin": "1000X", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "1INCHUSDT", "baseCoin": "1INCH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "A8USDT", "baseCoin": "A8", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AAVEUSDT", "baseCoin": "AAVE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ACEUSDT", "baseCoin": "ACE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ACHUSDT", "baseCoin": "ACH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ACTUSDT", "baseCoin": "ACT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ACXUSDT", "baseCoin": "ACX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ADAUSDT", "baseCoin": "ADA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AERGOUSDT", "baseCoin": "AERGO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AEROUSDT", "baseCoin": "AERO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AEVOUSDT", "baseCoin": "AEVO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AGIUSDT", "baseCoin": "AGI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AGLDUSDT", "baseCoin": "AGLD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AGTUSDT", "baseCoin": "AGT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AI16ZUSDT", "baseCoin": "AI16Z", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AIOZUSDT", "baseCoin": "AIOZ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AIUSDT", "baseCoin": "AI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AIXBTUSDT", "baseCoin": "AIXBT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AKTUSDT", "baseCoin": "AKT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALCHUSDT", "baseCoin": "ALCH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALEOUSDT", "baseCoin": "ALEO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALGOUSDT", "baseCoin": "ALGO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALICEUSDT", "baseCoin": "ALICE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALPHAUSDT", "baseCoin": "ALPHA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALTUSDT", "baseCoin": "ALT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ALUUSDT", "baseCoin": "ALU", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ANIMEUSDT", "baseCoin": "ANIME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ANKRUSDT", "baseCoin": "ANKR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "APEUSDT", "baseCoin": "APE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "API3USDT", "baseCoin": "API3", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "APTUSDT", "baseCoin": "APT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARBUSDT", "baseCoin": "ARB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARCUSDT", "baseCoin": "ARC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARKMUSDT", "baseCoin": "ARKM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARKUSDT", "baseCoin": "ARK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARPAUSDT", "baseCoin": "ARPA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ARUSDT", "baseCoin": "AR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ASTRUSDT", "baseCoin": "ASTR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ATAUSDT", "baseCoin": "ATA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ATHUSDT", "baseCoin": "ATH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ATOMUSDT", "baseCoin": "ATOM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AUCTIONUSDT", "baseCoin": "AUCTION", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AUDIOUSDT", "baseCoin": "AUDIO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AUSDT", "baseCoin": "A", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AVAAIUSDT", "baseCoin": "AVAAI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AVAILUSDT", "baseCoin": "AVAIL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AVAUSDT", "baseCoin": "AVA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AVAXUSDT", "baseCoin": "AVAX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AVLUSDT", "baseCoin": "AVL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AWEUSDT", "baseCoin": "AWE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AXLUSDT", "baseCoin": "AXL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "AXSUSDT", "baseCoin": "AXS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "B2USDT", "baseCoin": "B2", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "B3USDT", "baseCoin": "B3", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BABYUSDT", "baseCoin": "BABY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BADGERUSDT", "baseCoin": "BADGER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BAKEUSDT", "baseCoin": "BAKE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BALUSDT", "baseCoin": "BAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BANANAS31USDT", "baseCoin": "BANANAS31", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BANANAUSDT", "baseCoin": "BANANA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BANDUSDT", "baseCoin": "BAND", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BANKUSDT", "baseCoin": "BANK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BANUSDT", "baseCoin": "BAN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BATUSDT", "baseCoin": "BAT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BBUSDT", "baseCoin": "BB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BCHUSDT", "baseCoin": "BCH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BDXNUSDT", "baseCoin": "BDXN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BEAMUSDT", "baseCoin": "BEAM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BELUSDT", "baseCoin": "BEL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BERAUSDT", "baseCoin": "BERA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BICOUSDT", "baseCoin": "BICO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BIGTIMEUSDT", "baseCoin": "BIGTIME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BIOUSDT", "baseCoin": "BIO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BLASTUSDT", "baseCoin": "BLAST", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BLURUSDT", "baseCoin": "BLUR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BMTUSDT", "baseCoin": "BMT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BNBUSDT", "baseCoin": "BNB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BNTUSDT", "baseCoin": "BNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BOBAUSDT", "baseCoin": "BOBA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BOMEUSDT", "baseCoin": "BOME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BRETTUSDT", "baseCoin": "BRETT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BROCCOLIUSDT", "baseCoin": "BROCCOLI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BRUSDT", "baseCoin": "BR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BSVUSDT", "baseCoin": "BSV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BSWUSDT", "baseCoin": "BSW", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BTCUSDT", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "BTCUSDT-04JUL25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-20JUN25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-25JUL25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-26DEC25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-26SEP25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-27JUN25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-27MAR26", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BTCUSDT-29AUG25", "baseCoin": "BTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "BUSDT", "baseCoin": "B", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "C98USDT", "baseCoin": "C98", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CAKEUSDT", "baseCoin": "CAKE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CARVUSDT", "baseCoin": "CARV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CATIUSDT", "baseCoin": "CATI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CELOUSDT", "baseCoin": "CELO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CELRUSDT", "baseCoin": "CELR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CETUSUSDT", "baseCoin": "CETUS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CFXUSDT", "baseCoin": "CFX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CGPTUSDT", "baseCoin": "CGPT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CHESSUSDT", "baseCoin": "CHESS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CHILLGUYUSDT", "baseCoin": "CHILLGUY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CHRUSDT", "baseCoin": "CHR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CHZUSDT", "baseCoin": "CHZ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CKBUSDT", "baseCoin": "CKB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CLANKERUSDT", "baseCoin": "CLANKER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CLOUDUSDT", "baseCoin": "CLOUD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COMPUSDT", "baseCoin": "COMP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COOKIEUSDT", "baseCoin": "COOKIE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COOKUSDT", "baseCoin": "COOK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COREUSDT", "baseCoin": "CORE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COSUSDT", "baseCoin": "COS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COTIUSDT", "baseCoin": "COTI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "COWUSDT", "baseCoin": "COW", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CPOOLUSDT", "baseCoin": "CPOOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CROUSDT", "baseCoin": "CRO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CRVUSDT", "baseCoin": "CRV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CTCUSDT", "baseCoin": "CTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CTKUSDT", "baseCoin": "CTK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CTSIUSDT", "baseCoin": "CTSI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CUDISUSDT", "baseCoin": "CUDIS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CVCUSDT", "baseCoin": "CVC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CVXUSDT", "baseCoin": "CVX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "CYBERUSDT", "baseCoin": "CYBER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DARKUSDT", "baseCoin": "DARK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DASHUSDT", "baseCoin": "DASH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DBRUSDT", "baseCoin": "DBR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DEEPUSDT", "baseCoin": "DEEP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DEGENUSDT", "baseCoin": "DEGEN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DENTUSDT", "baseCoin": "DENT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DEXEUSDT", "baseCoin": "DEXE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DGBUSDT", "baseCoin": "DGB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DODOUSDT", "baseCoin": "DODO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DOGEUSDT", "baseCoin": "DOGE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DOGSUSDT", "baseCoin": "DOGS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DOGUSDT", "baseCoin": "DOG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DOODUSDT", "baseCoin": "DOOD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DOTUSDT", "baseCoin": "DOT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DRIFTUSDT", "baseCoin": "DRIFT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DUCKUSDT", "baseCoin": "DUCK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DUSKUSDT", "baseCoin": "DUSK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DYDXUSDT", "baseCoin": "DYDX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "DYMUSDT", "baseCoin": "DYM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "EDUUSDT", "baseCoin": "EDU", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "EGLDUSDT", "baseCoin": "EGLD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "EIGENUSDT", "baseCoin": "EIGEN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ELXUSDT", "baseCoin": "ELX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ENAUSDT", "baseCoin": "ENA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ENJUSDT", "baseCoin": "ENJ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ENSUSDT", "baseCoin": "ENS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "EPICUSDT", "baseCoin": "EPIC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "EPTUSDT", "baseCoin": "EPT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ETCUSDT", "baseCoin": "ETC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ETHBTCUSDT", "baseCoin": "ETHBTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ETHFIUSDT", "baseCoin": "ETHFI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ETHUSDT", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ETHUSDT-04JUL25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-20JUN25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-25JUL25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-26DEC25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-26SEP25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-27JUN25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-27MAR26", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHUSDT-29AUG25", "baseCoin": "ETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "ETHWUSDT", "baseCoin": "ETHW", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FARTCOINUSDT", "baseCoin": "FARTCOIN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FBUSDT", "baseCoin": "FB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FHEUSDT", "baseCoin": "FHE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FIDAUSDT", "baseCoin": "FIDA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FILUSDT", "baseCoin": "FIL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FIOUSDT", "baseCoin": "FIO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FLMUSDT", "baseCoin": "FLM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FLOCKUSDT", "baseCoin": "FLOCK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FLOWUSDT", "baseCoin": "FLOW", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FLRUSDT", "baseCoin": "FLR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FLUXUSDT", "baseCoin": "FLUX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FORMUSDT", "baseCoin": "FORM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FORTHUSDT", "baseCoin": "FORTH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FTNUSDT", "baseCoin": "FTN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FUELUSDT", "baseCoin": "FUEL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FUSDT", "baseCoin": "F", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FWOGUSDT", "baseCoin": "FWOG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "FXSUSDT", "baseCoin": "FXS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GALAUSDT", "baseCoin": "GALA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GASUSDT", "baseCoin": "GAS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GIGAUSDT", "baseCoin": "GIGA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GLMRUSDT", "baseCoin": "GLMR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GLMUSDT", "baseCoin": "GLM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GMTUSDT", "baseCoin": "GMT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GMXUSDT", "baseCoin": "GMX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GNOUSDT", "baseCoin": "GNO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GOATUSDT", "baseCoin": "GOAT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GODSUSDT", "baseCoin": "GODS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GORKUSDT", "baseCoin": "GORK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GPSUSDT", "baseCoin": "GPS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GRASSUSDT", "baseCoin": "GRASS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GRIFFAINUSDT", "baseCoin": "GRIFFAIN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GRTUSDT", "baseCoin": "GRT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GTCUSDT", "baseCoin": "GTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GUNUSDT", "baseCoin": "GUN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "GUSDT", "baseCoin": "G", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HAEDALUSDT", "baseCoin": "HAEDAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HBARUSDT", "baseCoin": "HBAR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HEIUSDT", "baseCoin": "HEI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HFTUSDT", "baseCoin": "HFT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HIFIUSDT", "baseCoin": "HIFI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HIGHUSDT", "baseCoin": "HIGH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HIPPOUSDT", "baseCoin": "HIPPO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HIVEUSDT", "baseCoin": "HIVE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HMSTRUSDT", "baseCoin": "HMSTR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HNTUSDT", "baseCoin": "HNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HOMEUSDT", "baseCoin": "HOME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HOOKUSDT", "baseCoin": "HOOK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HOTUSDT", "baseCoin": "HOT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HPOS10IUSDT", "baseCoin": "HPOS10I", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HUMAUSDT", "baseCoin": "HUMA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HYPERUSDT", "baseCoin": "HYPER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "HYPEUSDT", "baseCoin": "HYPE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ICPUSDT", "baseCoin": "ICP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ICXUSDT", "baseCoin": "ICX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IDEXUSDT", "baseCoin": "IDEX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IDUSDT", "baseCoin": "ID", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ILVUSDT", "baseCoin": "ILV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IMXUSDT", "baseCoin": "IMX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "INITUSDT", "baseCoin": "INIT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "INJUSDT", "baseCoin": "INJ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IOSTUSDT", "baseCoin": "IOST", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IOTAUSDT", "baseCoin": "IOTA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IOTXUSDT", "baseCoin": "IOTX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IOUSDT", "baseCoin": "IO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "IPUSDT", "baseCoin": "IP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JASMYUSDT", "baseCoin": "JASMY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JELLYJELLYUSDT", "baseCoin": "JELLYJELLY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JOEUSDT", "baseCoin": "JOE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JSTUSDT", "baseCoin": "JST", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JTOUSDT", "baseCoin": "JTO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JUPUSDT", "baseCoin": "JUP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "JUSDT", "baseCoin": "J", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KAIAUSDT", "baseCoin": "KAIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KAITOUSDT", "baseCoin": "KAITO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KASUSDT", "baseCoin": "KAS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KAVAUSDT", "baseCoin": "KAVA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KDAUSDT", "baseCoin": "KDA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KERNELUSDT", "baseCoin": "KERNEL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KMNOUSDT", "baseCoin": "KMNO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KNCUSDT", "baseCoin": "KNC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KOMAUSDT", "baseCoin": "KOMA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "KSMUSDT", "baseCoin": "KSM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "L3USDT", "baseCoin": "L3", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LAUNCHCOINUSDT", "baseCoin": "LAUNCHCOIN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LAUSDT", "baseCoin": "LA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LDOUSDT", "baseCoin": "LDO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LEVERUSDT", "baseCoin": "LEVER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LINKUSDT", "baseCoin": "LINK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LISTAUSDT", "baseCoin": "LISTA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LOOKSUSDT", "baseCoin": "LOOKS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LPTUSDT", "baseCoin": "LPT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LQTYUSDT", "baseCoin": "LQTY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LRCUSDT", "baseCoin": "LRC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LSKUSDT", "baseCoin": "LSK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LTCUSDT", "baseCoin": "LTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LUMIAUSDT", "baseCoin": "LUMIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "LUNA2USDT", "baseCoin": "LUNA2", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MAGICUSDT", "baseCoin": "MAGIC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MAJORUSDT", "baseCoin": "MAJOR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MANAUSDT", "baseCoin": "MANA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MANTAUSDT", "baseCoin": "MANTA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MASAUSDT", "baseCoin": "MASA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MASKUSDT", "baseCoin": "MASK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MAVIAUSDT", "baseCoin": "MAVIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MAVUSDT", "baseCoin": "MAV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MBLUSDT", "baseCoin": "MBL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MBOXUSDT", "baseCoin": "MBOX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MDTUSDT", "baseCoin": "MDT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MELANIAUSDT", "baseCoin": "MELANIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MEMEUSDT", "baseCoin": "MEME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MERLUSDT", "baseCoin": "MERL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "METISUSDT", "baseCoin": "METIS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MEUSDT", "baseCoin": "ME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MEWUSDT", "baseCoin": "MEW", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MICHIUSDT", "baseCoin": "MICHI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MILKUSDT", "baseCoin": "MILK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MINAUSDT", "baseCoin": "MINA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MKRUSDT", "baseCoin": "MKR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MLNUSDT", "baseCoin": "MLN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MNTUSDT", "baseCoin": "MNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MOBILEUSDT", "baseCoin": "MOBILE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MOCAUSDT", "baseCoin": "MOCA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MOODENGUSDT", "baseCoin": "MOODENG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MORPHOUSDT", "baseCoin": "MORPHO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MOVEUSDT", "baseCoin": "MOVE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MOVRUSDT", "baseCoin": "MOVR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MTLUSDT", "baseCoin": "MTL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MUBARAKUSDT", "baseCoin": "MUBARAK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MVLUSDT", "baseCoin": "MVL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MYRIAUSDT", "baseCoin": "MYRIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "MYROUSDT", "baseCoin": "MYRO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NCUSDT", "baseCoin": "NC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NEARUSDT", "baseCoin": "NEAR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NEIROETHUSDT", "baseCoin": "NEIROETH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NEOUSDT", "baseCoin": "NEO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NFPUSDT", "baseCoin": "NFP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NILUSDT", "baseCoin": "NIL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NKNUSDT", "baseCoin": "NKN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NMRUSDT", "baseCoin": "NMR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NOTUSDT", "baseCoin": "NOT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NSUSDT", "baseCoin": "NS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NTRNUSDT", "baseCoin": "NTRN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "NXPCUSDT", "baseCoin": "NXPC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OBOLUSDT", "baseCoin": "OBOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OBTUSDT", "baseCoin": "OBT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OGNUSDT", "baseCoin": "OGN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OGUSDT", "baseCoin": "OG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OLUSDT", "baseCoin": "OL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OMNIUSDT", "baseCoin": "OMNI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OMUSDT", "baseCoin": "OM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ONDOUSDT", "baseCoin": "ONDO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ONEUSDT", "baseCoin": "ONE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ONGUSDT", "baseCoin": "ONG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ONTUSDT", "baseCoin": "ONT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OPUSDT", "baseCoin": "OP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ORBSUSDT", "baseCoin": "ORBS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ORCAUSDT", "baseCoin": "ORCA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ORDERUSDT", "baseCoin": "ORDER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ORDIUSDT", "baseCoin": "ORDI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OSMOUSDT", "baseCoin": "OSMO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "OXTUSDT", "baseCoin": "OXT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PARTIUSDT", "baseCoin": "PARTI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PAXGUSDT", "baseCoin": "PAXG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PEAQUSDT", "baseCoin": "PEAQ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PENDLEUSDT", "baseCoin": "PENDLE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PENGUUSDT", "baseCoin": "PENGU", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PEOPLEUSDT", "baseCoin": "PEOPLE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PERPUSDT", "baseCoin": "PERP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PHAUSDT", "baseCoin": "PHA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PHBUSDT", "baseCoin": "PHB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PIPPINUSDT", "baseCoin": "PIPPIN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PIXELUSDT", "baseCoin": "PIXEL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PLUMEUSDT", "baseCoin": "PLUME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PNUTUSDT", "baseCoin": "PNUT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "POLUSDT", "baseCoin": "POL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "POLYXUSDT", "baseCoin": "POLYX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PONKEUSDT", "baseCoin": "PONKE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "POPCATUSDT", "baseCoin": "POPCAT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PORTALUSDT", "baseCoin": "PORTAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "POWRUSDT", "baseCoin": "POWR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PRAIUSDT", "baseCoin": "PRAI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PRCLUSDT", "baseCoin": "PRCL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PRIMEUSDT", "baseCoin": "PRIME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PROMPTUSDT", "baseCoin": "PROMPT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PROMUSDT", "baseCoin": "PROM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PUFFERUSDT", "baseCoin": "PUFFER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PUMPBTCUSDT", "baseCoin": "PUMPBTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PUNDIXUSDT", "baseCoin": "PUNDIX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PYRUSDT", "baseCoin": "PYR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "PYTHUSDT", "baseCoin": "PYTH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "QIUSDT", "baseCoin": "QI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "QNTUSDT", "baseCoin": "QNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "QTUMUSDT", "baseCoin": "QTUM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "QUICKUSDT", "baseCoin": "QUICK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RADUSDT", "baseCoin": "RAD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RAREUSDT", "baseCoin": "RARE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RAYDIUMUSDT", "baseCoin": "RAYDIUM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RDNTUSDT", "baseCoin": "RDNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "REDUSDT", "baseCoin": "RED", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RENDERUSDT", "baseCoin": "RENDER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "REQUSDT", "baseCoin": "REQ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RESOLVUSDT", "baseCoin": "RESOLV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "REXUSDT", "baseCoin": "REX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "REZUSDT", "baseCoin": "REZ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RFCUSDT", "baseCoin": "RFC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RIFUSDT", "baseCoin": "RIF", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RLCUSDT", "baseCoin": "RLC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ROAMUSDT", "baseCoin": "ROAM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RONINUSDT", "baseCoin": "RONIN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ROSEUSDT", "baseCoin": "ROSE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RPLUSDT", "baseCoin": "RPL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RSRUSDT", "baseCoin": "RSR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RSS3USDT", "baseCoin": "RSS3", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RUNEUSDT", "baseCoin": "RUNE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "RVNUSDT", "baseCoin": "RVN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SAFEUSDT", "baseCoin": "SAFE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SAGAUSDT", "baseCoin": "SAGA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SANDUSDT", "baseCoin": "SAND", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SAROSUSDT", "baseCoin": "SAROS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SCAUSDT", "baseCoin": "SCA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SCRTUSDT", "baseCoin": "SCRT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SCRUSDT", "baseCoin": "SCR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SCUSDT", "baseCoin": "SC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SDUSDT", "baseCoin": "SD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SEIUSDT", "baseCoin": "SEI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SENDUSDT", "baseCoin": "SEND", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SERAPHUSDT", "baseCoin": "SERAPH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SFPUSDT", "baseCoin": "SFP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SHELLUSDT", "baseCoin": "SHELL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SHIB1000USDT", "baseCoin": "SHIB1000", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SIGNUSDT", "baseCoin": "SIGN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SIRENUSDT", "baseCoin": "SIREN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SKATEUSDT", "baseCoin": "SKATE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SKLUSDT", "baseCoin": "SKL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SKYAIUSDT", "baseCoin": "SKYAI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SLERFUSDT", "baseCoin": "SLERF", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SLFUSDT", "baseCoin": "SLF", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SLPUSDT", "baseCoin": "SLP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SNTUSDT", "baseCoin": "SNT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SNXUSDT", "baseCoin": "SNX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOLAYERUSDT", "baseCoin": "SOLAYER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOLOUSDT", "baseCoin": "SOLO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOLUSDT", "baseCoin": "SOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOLUSDT-04JUL25", "baseCoin": "SOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "SOLUSDT-20JUN25", "baseCoin": "SOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "SOLUSDT-25JUL25", "baseCoin": "SOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "SOLUSDT-27JUN25", "baseCoin": "SOL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearFutures"}, {"symbol": "SOLVUSDT", "baseCoin": "SOLV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SONICUSDT", "baseCoin": "SONIC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOONUSDT", "baseCoin": "SOON", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SOPHUSDT", "baseCoin": "SOPH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SPECUSDT", "baseCoin": "SPEC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SPELLUSDT", "baseCoin": "SPELL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SPXUSDT", "baseCoin": "SPX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SQDUSDT", "baseCoin": "SQD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SSVUSDT", "baseCoin": "SSV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STEEMUSDT", "baseCoin": "STEEM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STGUSDT", "baseCoin": "STG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STORJUSDT", "baseCoin": "STORJ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STOUSDT", "baseCoin": "STO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STRKUSDT", "baseCoin": "STRK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "STXUSDT", "baseCoin": "STX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUIUSDT", "baseCoin": "SUI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUNDOGUSDT", "baseCoin": "SUNDOG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUNUSDT", "baseCoin": "SUN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUPERUSDT", "baseCoin": "SUPER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUSDT", "baseCoin": "S", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SUSHIUSDT", "baseCoin": "SUSHI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SWARMSUSDT", "baseCoin": "SWARMS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SWEATUSDT", "baseCoin": "SWEAT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SWELLUSDT", "baseCoin": "SWELL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SXPUSDT", "baseCoin": "SXP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SXTUSDT", "baseCoin": "SXT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SYNUSDT", "baseCoin": "SYN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SYRUPUSDT", "baseCoin": "SYRUP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "SYSUSDT", "baseCoin": "SYS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TAIKOUSDT", "baseCoin": "TAIKO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TAIUSDT", "baseCoin": "TAI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TAOUSDT", "baseCoin": "TAO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "THETAUSDT", "baseCoin": "THETA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "THEUSDT", "baseCoin": "THE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TIAUSDT", "baseCoin": "TIA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TLMUSDT", "baseCoin": "TLM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TNSRUSDT", "baseCoin": "TNSR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TOKENUSDT", "baseCoin": "TOKEN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TONUSDT", "baseCoin": "TON", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TRBUSDT", "baseCoin": "TRB", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TRUMPUSDT", "baseCoin": "TRUMP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TRUUSDT", "baseCoin": "TRU", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TRXUSDT", "baseCoin": "TRX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TSTBSCUSDT", "baseCoin": "TSTBSC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TUSDT", "baseCoin": "T", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TUTUSDT", "baseCoin": "TUT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "TWTUSDT", "baseCoin": "TWT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "UMAUSDT", "baseCoin": "UMA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "UNIUSDT", "baseCoin": "UNI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "USDCUSDT", "baseCoin": "USDC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "USDEUSDT", "baseCoin": "USDE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "USTCUSDT", "baseCoin": "USTC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "USUALUSDT", "baseCoin": "USUAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "UXLINKUSDT", "baseCoin": "UXLINK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VANAUSDT", "baseCoin": "VANA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VANRYUSDT", "baseCoin": "VANRY", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VELODROMEUSDT", "baseCoin": "VELODROME", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VELOUSDT", "baseCoin": "VELO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VETUSDT", "baseCoin": "VET", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VICUSDT", "baseCoin": "VIC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VINEUSDT", "baseCoin": "VINE", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VIRTUALUSDT", "baseCoin": "VIRTUAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VOXELUSDT", "baseCoin": "VOXEL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VRUSDT", "baseCoin": "VR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VTHOUSDT", "baseCoin": "VTHO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "VVVUSDT", "baseCoin": "VVV", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WALUSDT", "baseCoin": "WAL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WAVESUSDT", "baseCoin": "WAVES", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WAXPUSDT", "baseCoin": "WAXP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WCTUSDT", "baseCoin": "WCT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WIFUSDT", "baseCoin": "WIF", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WLDUSDT", "baseCoin": "WLD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WOOUSDT", "baseCoin": "WOO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "WUSDT", "baseCoin": "W", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XAIUSDT", "baseCoin": "XAI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XAUTUSDT", "baseCoin": "XAUT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XCHUSDT", "baseCoin": "XCH", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XCNUSDT", "baseCoin": "XCN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XDCUSDT", "baseCoin": "XDC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XEMUSDT", "baseCoin": "XEM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XIONUSDT", "baseCoin": "XION", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XLMUSDT", "baseCoin": "XLM", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XMRUSDT", "baseCoin": "XMR", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XNOUSDT", "baseCoin": "XNO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XRDUSDT", "baseCoin": "XRD", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XRPUSDT", "baseCoin": "XRP", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XTERUSDT", "baseCoin": "XTER", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XTZUSDT", "baseCoin": "XTZ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XVGUSDT", "baseCoin": "XVG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "XVSUSDT", "baseCoin": "XVS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "YFIUSDT", "baseCoin": "YFI", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "YGGUSDT", "baseCoin": "YGG", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZBCNUSDT", "baseCoin": "ZBCN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZECUSDT", "baseCoin": "ZEC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZENTUSDT", "baseCoin": "ZENT", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZENUSDT", "baseCoin": "ZEN", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZEREBROUSDT", "baseCoin": "ZEREBRO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZETAUSDT", "baseCoin": "ZETA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZEUSUSDT", "baseCoin": "ZEUS", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZILUSDT", "baseCoin": "ZIL", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZKJUSDT", "baseCoin": "ZKJ", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZKUSDT", "baseCoin": "ZK", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZORAUSDT", "baseCoin": "ZORA", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZRCUSDT", "baseCoin": "ZRC", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZROUSDT", "baseCoin": "ZRO", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}, {"symbol": "ZRXUSDT", "baseCoin": "ZRX", "quoteCoin": "USDT", "status": "Trading", "contractType": "LinearPerpetual"}], "lastUpdate": 1749829262102}, "weights": {"1000PEPEUSDT": {"symbol": "1000PEPEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5747119067579349, "confidence": 0.42000000000000004, "performance": {"winRate": 0.75, "avgReturn": 0.002672431406031782, "sharpeRatio": 0.040830550809743765, "consistency": 0.5}, "lastUpdated": 1749829450975}, "MACD": {"indicator": "MACD", "weight": 0.428078753885277, "confidence": 0.4138518518518519, "performance": {"winRate": 0.37037037037037035, "avgReturn": -0.005927503007734796, "sharpeRatio": -0.14421414450996814, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5326950034276805, "confidence": 0.38676923076923075, "performance": {"winRate": 0.5769230769230769, "avgReturn": 0.0017281224593341654, "sharpeRatio": 0.05104114475420555, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.2011425556456311, "lastUpdated": 1749829450976}, "DOGSUSDT": {"symbol": "DOGSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.4801645156540942, "confidence": 0.34, "performance": {"winRate": 0.55, "avgReturn": -0.007068416673829549, "sharpeRatio": -0.11140307637003603, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.41753219959574617, "confidence": 0.42514285714285716, "performance": {"winRate": 0.35714285714285715, "avgReturn": -0.006961586701553654, "sharpeRatio": -0.16876064367975468, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.4457991417944048, "confidence": 0.33552380952380956, "performance": {"winRate": 0.47619047619047616, "avgReturn": -0.009302575944405473, "sharpeRatio": -0.17518853403036314, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.1675857484862432, "lastUpdated": 1749829450976}, "HMSTRUSDT": {"symbol": "HMSTRUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6565739744449135, "confidence": 0.41000000000000003, "performance": {"winRate": 0.8, "avgReturn": 0.010011753728462226, "sharpeRatio": 0.3448739191086182, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.34338066159068736, "confidence": 0.4733333333333334, "performance": {"winRate": 0.26666666666666666, "avgReturn": -0.013200051540025126, "sharpeRatio": -0.37641608974477037, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.7305158171650427, "confidence": 0.4410909090909091, "performance": {"winRate": 0.7727272727272727, "avgReturn": 0.01137170966397466, "sharpeRatio": 0.6453029924797761, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.27983168926940644, "confidence": 0.3932727272727273, "performance": {"winRate": 0.18181818181818182, "avgReturn": -0.012492686136561041, "sharpeRatio": -0.800418130778267, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.21600072504756768, "lastUpdated": 1749829450976}, "10000SATSUSDT": {"symbol": "10000SATSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.4475504524421836, "confidence": 0.32452631578947366, "performance": {"winRate": 0.5263157894736842, "avgReturn": -0.014044743996292061, "sharpeRatio": -0.19177525304942414, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.43790711057272164, "confidence": 0.38, "performance": {"winRate": 0.35, "avgReturn": -0.0044163285037865764, "sharpeRatio": -0.09290846645767888, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.47640901858698137, "confidence": 0.32452631578947366, "performance": {"winRate": 0.5263157894736842, "avgReturn": -0.005938823996657518, "sharpeRatio": -0.11364474864875837, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.16156346627238, "lastUpdated": 1749829450976}, "NOTUSDT": {"symbol": "NOTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5272938213794844, "confidence": 0.36, "performance": {"winRate": 0.6, "avgReturn": 0.0004172171171611054, "sharpeRatio": 0.00836132301002822, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.46738300675374783, "confidence": 0.3990370370370371, "performance": {"winRate": 0.4074074074074074, "avgReturn": -0.0011218095055333475, "sharpeRatio": -0.03729493007324347, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.45649435016037404, "confidence": 0.358, "performance": {"winRate": 0.52, "avgReturn": -0.0083795479388191, "sharpeRatio": -0.1656500340302521, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.17993847080762632, "lastUpdated": 1749829450976}, "BOMEUSDT": {"symbol": "BOMEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5194247916027097, "confidence": 0.37361904761904763, "performance": {"winRate": 0.6190476190476191, "avgReturn": -0.0022647658106815156, "sharpeRatio": -0.032024329203477725, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.472397416181769, "confidence": 0.38422222222222224, "performance": {"winRate": 0.4444444444444444, "avgReturn": -0.002068266257539015, "sharpeRatio": -0.04848449804761984, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5593876493280775, "confidence": 0.42200000000000004, "performance": {"winRate": 0.68, "avgReturn": 0.0026517316495379395, "sharpeRatio": 0.04586179991669973, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.19790854224554266, "lastUpdated": 1749829450976}, "1000BONKUSDT": {"symbol": "1000BONKUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5359942647082673, "confidence": 0.39266666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": -0.001073714809030417, "sharpeRatio": -0.02026289103625019, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.4741311637349458, "confidence": 0.3713846153846154, "performance": {"winRate": 0.5384615384615384, "avgReturn": -0.004814827010973337, "sharpeRatio": -0.13967304139526376, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.4868024904418998, "confidence": 0.38676923076923075, "performance": {"winRate": 0.5769230769230769, "avgReturn": -0.00568498730742363, "sharpeRatio": -0.10534538785339151, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.18870808148144214, "lastUpdated": 1749829450976}, "GALAUSDT": {"symbol": "GALAUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5492896260403908, "confidence": 0.37361904761904763, "performance": {"winRate": 0.6190476190476191, "avgReturn": 0.003286634572288714, "sharpeRatio": 0.07435304935216026, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.37539432832522945, "confidence": 0.47950000000000004, "performance": {"winRate": 0.28125, "avgReturn": -0.007281825090175636, "sharpeRatio": -0.29592543064677024, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5448895187749503, "confidence": 0.39, "performance": {"winRate": 0.6, "avgReturn": 0.0026084296016688035, "sharpeRatio": 0.07754607499293546, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.19443338992560294, "lastUpdated": 1749829450976}, "MEWUSDT": {"symbol": "MEWUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5441452316050893, "confidence": 0.41171428571428575, "performance": {"winRate": 0.7142857142857143, "avgReturn": -0.0019062519952450236, "sharpeRatio": -0.0314393390218003, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.555586321037302, "confidence": 0.356, "performance": {"winRate": 0.5, "avgReturn": 0.008468574412107887, "sharpeRatio": 0.1920395908744179, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.429285763772346, "confidence": 0.33022222222222225, "performance": {"winRate": 0.4444444444444444, "avgReturn": -0.012173360217584514, "sharpeRatio": -0.21110843188654826, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.18589519863141785, "lastUpdated": 1749829450976}, "ANIMEUSDT": {"symbol": "ANIMEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.43644104112328114, "confidence": 0.32452631578947366, "performance": {"winRate": 0.5263157894736842, "avgReturn": -0.02063381376453449, "sharpeRatio": -0.18908051070750648, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.5489157592815216, "confidence": 0.36408695652173917, "performance": {"winRate": 0.43478260869565216, "avgReturn": 0.014467264059274855, "sharpeRatio": 0.18081497366198193, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.48984051993468475, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": -0.012470456288693052, "sharpeRatio": -0.1326419260932189, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.1806627112096333, "lastUpdated": 1749829450976}, "RVNUSDT": {"symbol": "RVNUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6375409046158403, "confidence": 0.42200000000000004, "performance": {"winRate": 0.68, "avgReturn": 0.02565240427898984, "sharpeRatio": 0.23124020080411478, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.4298034616917584, "confidence": 0.406, "performance": {"winRate": 0.36, "avgReturn": -0.006710135767586745, "sharpeRatio": -0.11319944757793433, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6218235111722921, "confidence": 0.4021538461538462, "performance": {"winRate": 0.6153846153846154, "avgReturn": 0.021486756474117852, "sharpeRatio": 0.21514734137848138, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.21840279596039125, "lastUpdated": 1749829450976}, "SHIB1000USDT": {"symbol": "SHIB1000USDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5614918283774218, "confidence": 0.4047272727272727, "performance": {"winRate": 0.6818181818181818, "avgReturn": 0.0019464222425528033, "sharpeRatio": 0.07096340437854584, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.45382724909799343, "confidence": 0.4021538461538462, "performance": {"winRate": 0.38461538461538464, "avgReturn": -0.0013293376361270452, "sharpeRatio": -0.0818919605196553, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5933149356462192, "confidence": 0.43292307692307697, "performance": {"winRate": 0.6923076923076923, "avgReturn": 0.004966480714077474, "sharpeRatio": 0.18841527525404678, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.28452653714582576, "confidence": 0.3932727272727273, "performance": {"winRate": 0.18181818181818182, "avgReturn": -0.013420525136865093, "sharpeRatio": -0.7623149922665753, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.19462892121030098, "lastUpdated": 1749829450976}, "PENGUUSDT": {"symbol": "PENGUUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5652508091580069, "confidence": 0.42733333333333334, "performance": {"winRate": 0.7083333333333334, "avgReturn": 0.0024644664865145012, "sharpeRatio": 0.04400947497978825, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.4594642246634739, "confidence": 0.3865454545454546, "performance": {"winRate": 0.36363636363636365, "avgReturn": -0.0011301699004427962, "sharpeRatio": -0.0229191034537586, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.3546228996517728, "confidence": 0.3865454545454546, "performance": {"winRate": 0.36363636363636365, "avgReturn": -0.021526885342795832, "sharpeRatio": -0.40345301729384275, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.1840580474887235, "lastUpdated": 1749829450976}, "DOGEUSDT": {"symbol": "DOGEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5528660966681558, "confidence": 0.42733333333333334, "performance": {"winRate": 0.7083333333333334, "avgReturn": 0.00011276198911198965, "sharpeRatio": 0.0030230728354234257, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.41828171520554847, "confidence": 0.4175384615384615, "performance": {"winRate": 0.34615384615384615, "avgReturn": -0.004787517375357004, "sharpeRatio": -0.19165200067001634, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5262579256651019, "confidence": 0.38147826086956527, "performance": {"winRate": 0.6086956521739131, "avgReturn": -0.00011618235982328213, "sharpeRatio": -0.0038318978661909457, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.33322579410937997, "confidence": 0.33999999999999997, "performance": {"winRate": 0.3, "avgReturn": -0.009358206995290224, "sharpeRatio": -0.6736584544179317, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.18123988602038069, "lastUpdated": 1749829450976}, "MOBILEUSDT": {"symbol": "MOBILEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5861186689852848, "confidence": 0.4047272727272727, "performance": {"winRate": 0.6818181818181818, "avgReturn": 0.00640144546309123, "sharpeRatio": 0.1554994926372687, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.34124843048469483, "confidence": 0.5071428571428571, "performance": {"winRate": 0.2571428571428571, "avgReturn": -0.01045542820714467, "sharpeRatio": -0.39537476949476574, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5320994678348292, "confidence": 0.3968888888888889, "performance": {"winRate": 0.7222222222222222, "avgReturn": -0.004522885305670383, "sharpeRatio": -0.09452064510764298, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.2003660702479529, "lastUpdated": 1749829450976}, "XVGUSDT": {"symbol": "XVGUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5490539543943185, "confidence": 0.38, "performance": {"winRate": 0.65, "avgReturn": 0.0020600748725279967, "sharpeRatio": 0.04358317048475921, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.5661969482734208, "confidence": 0.38676923076923075, "performance": {"winRate": 0.4230769230769231, "avgReturn": 0.012509863121202542, "sharpeRatio": 0.2835633939027501, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5883915337318462, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.006990351177352222, "sharpeRatio": 0.1755004417325061, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.2123152126674957, "lastUpdated": 1749829450976}, "MEMEUSDT": {"symbol": "MEMEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.46731380340061873, "confidence": 0.36, "performance": {"winRate": 0.6, "avgReturn": -0.011540389878341951, "sharpeRatio": -0.21175792257124737, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.40777143837598606, "confidence": 0.4394285714285715, "performance": {"winRate": 0.32142857142857145, "avgReturn": -0.006589241929374926, "sharpeRatio": -0.181625546094836, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5790254251283492, "confidence": 0.45371428571428574, "performance": {"winRate": 0.7142857142857143, "avgReturn": 0.004056625082692437, "sharpeRatio": 0.08436534949467774, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.31832110590592416, "confidence": 0.35690909090909095, "performance": {"winRate": 0.2727272727272727, "avgReturn": -0.017781455257488307, "sharpeRatio": -0.65984602414441, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.18093579838943388, "lastUpdated": 1749829450976}, "ZBCNUSDT": {"symbol": "ZBCNUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.4850554835660477, "confidence": 0.37466666666666665, "performance": {"winRate": 0.6666666666666666, "avgReturn": -0.019612765199318465, "sharpeRatio": -0.1310894949464876, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MACD": {"indicator": "MACD", "weight": 0.3710612248536704, "confidence": 0.42866666666666675, "performance": {"winRate": 0.3333333333333333, "avgReturn": -0.01932009082104234, "sharpeRatio": -0.23729339412477, "consistency": 0.5}, "lastUpdated": 1749829450976}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.45321630508276733, "confidence": 0.36, "performance": {"winRate": 0.6, "avgReturn": -0.02040784713053443, "sharpeRatio": -0.19286285099609354, "consistency": 0.5}, "lastUpdated": 1749829450976}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450976}}, "overallScore": 0.17098839234828722, "lastUpdated": 1749829450976}, "10000WHYUSDT": {"symbol": "10000WHYUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.3824419735935882, "confidence": 0.33022222222222225, "performance": {"winRate": 0.4444444444444444, "avgReturn": -0.023022722752247777, "sharpeRatio": -0.3889178708976605, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4547171772516058, "confidence": 0.4698709677419355, "performance": {"winRate": 0.2903225806451613, "avgReturn": 0.0010293672054087926, "sharpeRatio": 0.023674171734793897, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.40674544080854663, "confidence": 0.34, "performance": {"winRate": 0.45, "avgReturn": -0.01520477281627201, "sharpeRatio": -0.31388972609334087, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.16456067209753156, "lastUpdated": 1749829450977}, "SPELLUSDT": {"symbol": "SPELLUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6386314966973121, "confidence": 0.4336521739130435, "performance": {"winRate": 0.7391304347826086, "avgReturn": 0.010668644421979194, "sharpeRatio": 0.30737534563462654, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4764578035927783, "confidence": 0.36940740740740746, "performance": {"winRate": 0.48148148148148145, "avgReturn": -0.0018746690076072768, "sharpeRatio": -0.08263003416284237, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6263659485372294, "confidence": 0.4434814814814815, "performance": {"winRate": 0.7037037037037037, "avgReturn": 0.008152760969430857, "sharpeRatio": 0.30922122236000416, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.2671479065157797, "confidence": 0.3932727272727273, "performance": {"winRate": 0.18181818181818182, "avgReturn": -0.013655005864514042, "sharpeRatio": -0.8764530044641237, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20894866585596755, "lastUpdated": 1749829450977}, "ORBSUSDT": {"symbol": "ORBSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5979825823317152, "confidence": 0.4087368421052631, "performance": {"winRate": 0.7368421052631579, "avgReturn": 0.004191341133582853, "sharpeRatio": 0.18535408583532417, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4439168646957259, "confidence": 0.4021538461538462, "performance": {"winRate": 0.38461538461538464, "avgReturn": -0.00213903207981576, "sharpeRatio": -0.13241929303739258, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5911832829405385, "confidence": 0.4138518518518519, "performance": {"winRate": 0.6296296296296297, "avgReturn": 0.005183313268387567, "sharpeRatio": 0.2630440109339285, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.21190067081873942, "lastUpdated": 1749829450977}, "10000ELONUSDT": {"symbol": "10000ELONUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5082593172789993, "confidence": 0.3666315789473684, "performance": {"winRate": 0.631578947368421, "avgReturn": -0.003966111744107469, "sharpeRatio": -0.10434053844616129, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4460951201991959, "confidence": 0.406, "performance": {"winRate": 0.36, "avgReturn": -0.0027041885685211535, "sharpeRatio": -0.07412767290516681, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5227263732550261, "confidence": 0.4175384615384615, "performance": {"winRate": 0.6538461538461539, "avgReturn": -0.0019731820222407956, "sharpeRatio": -0.06577772745456016, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19142922512601696, "lastUpdated": 1749829450977}, "HIPPOUSDT": {"symbol": "HIPPOUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5784705408431347, "confidence": 0.39266666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.008403744424110966, "sharpeRatio": 0.11210692706420147, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.2931805026856389, "confidence": 0.5085806451612903, "performance": {"winRate": 0.1935483870967742, "avgReturn": -0.02329168746917554, "sharpeRatio": -0.5185815949989667, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.7141634792490884, "confidence": 0.4773333333333334, "performance": {"winRate": 0.8333333333333334, "avgReturn": 0.02983376421034542, "sharpeRatio": 0.45532476619570983, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.22428651558430235, "lastUpdated": 1749829450977}, "10000LADYSUSDT": {"symbol": "10000LADYSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5507576065740551, "confidence": 0.38147826086956527, "performance": {"winRate": 0.6086956521739131, "avgReturn": 0.0036788919383714445, "sharpeRatio": 0.08847036295196901, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.5225240378949424, "confidence": 0.33552380952380956, "performance": {"winRate": 0.47619047619047616, "avgReturn": 0.004399412644548552, "sharpeRatio": 0.10919573952806766, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.47946371262734566, "confidence": 0.34, "performance": {"winRate": 0.55, "avgReturn": -0.005836647770747404, "sharpeRatio": -0.12755398432682483, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.18210974299303173, "lastUpdated": 1749829450977}, "ZEREBROUSDT": {"symbol": "ZEREBROUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.7085047793382623, "confidence": 0.45400000000000007, "performance": {"winRate": 0.76, "avgReturn": 0.026386099977875818, "sharpeRatio": 0.4975004658088931, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4075474609376916, "confidence": 0.3990370370370371, "performance": {"winRate": 0.4074074074074074, "avgReturn": -0.010391105133982052, "sharpeRatio": -0.2575320440360384, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5447627924838436, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.0005373356237896946, "sharpeRatio": 0.010618261169395508, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.22200090529937005, "lastUpdated": 1749829450977}, "DENTUSDT": {"symbol": "DENTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5516257011200874, "confidence": 0.3666315789473684, "performance": {"winRate": 0.631578947368421, "avgReturn": 0.0032761726542917895, "sharpeRatio": 0.0731438224716265, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.42535299712810304, "confidence": 0.4394285714285715, "performance": {"winRate": 0.32142857142857145, "avgReturn": -0.003544885598677414, "sharpeRatio": -0.11599929749447735, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.47949718345741915, "confidence": 0.35018181818181815, "performance": {"winRate": 0.5454545454545454, "avgReturn": -0.004899428633269822, "sharpeRatio": -0.12344691636724583, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.18426671429666253, "lastUpdated": 1749829450977}, "LEVERUSDT": {"symbol": "LEVERUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5744618310977183, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.005055857818006984, "sharpeRatio": 0.12345791871184936, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4558044431707118, "confidence": 0.3990370370370371, "performance": {"winRate": 0.4074074074074074, "avgReturn": -0.002492402317182096, "sharpeRatio": -0.08595171071018895, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5194645426379676, "confidence": 0.3865454545454546, "performance": {"winRate": 0.6363636363636364, "avgReturn": -0.00227293850497914, "sharpeRatio": -0.05930998562242686, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19964795938238172, "lastUpdated": 1749829450977}, "1000000BABYDOGEUSDT": {"symbol": "1000000BABYDOGEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5688774670793522, "confidence": 0.3666315789473684, "performance": {"winRate": 0.631578947368421, "avgReturn": 0.00662060991100513, "sharpeRatio": 0.1336942218044709, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.42692649301519825, "confidence": 0.42, "performance": {"winRate": 0.4, "avgReturn": -0.005645878531590492, "sharpeRatio": -0.1955241358860418, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5430387185165092, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.00021218235984778077, "sharpeRatio": 0.005137584712345522, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.1977213678633447, "lastUpdated": 1749829450977}, "DOGUSDT": {"symbol": "DOGUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5092029891470918, "confidence": 0.36082352941176477, "performance": {"winRate": 0.6470588235294118, "avgReturn": -0.007774946590967315, "sharpeRatio": -0.0858299086767071, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4782295569509255, "confidence": 0.40848275862068967, "performance": {"winRate": 0.41379310344827586, "avgReturn": -3.8671421570591833e-05, "sharpeRatio": -0.0006439485447222667, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.4414991127018849, "confidence": 0.35457142857142854, "performance": {"winRate": 0.5714285714285714, "avgReturn": -0.019618311413894484, "sharpeRatio": -0.21263725141740236, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.17890597987801599, "lastUpdated": 1749829450977}, "PEOPLEUSDT": {"symbol": "PEOPLEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5390208112823289, "confidence": 0.36, "performance": {"winRate": 0.6, "avgReturn": 0.003244719571386488, "sharpeRatio": 0.04227185459510146, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4206461816762578, "confidence": 0.42514285714285716, "performance": {"winRate": 0.35714285714285715, "avgReturn": -0.0063184841578441025, "sharpeRatio": -0.16235600232784325, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5899208002280835, "confidence": 0.42200000000000004, "performance": {"winRate": 0.68, "avgReturn": 0.008350725350538846, "sharpeRatio": 0.14282508131899022, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20045719732049183, "lastUpdated": 1749829450977}, "BRETTUSDT": {"symbol": "BRETTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.57522145260624, "confidence": 0.3968888888888889, "performance": {"winRate": 0.7222222222222222, "avgReturn": 0.0048736736501490035, "sharpeRatio": 0.06566005625658951, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4784145561424318, "confidence": 0.36836363636363634, "performance": {"winRate": 0.4090909090909091, "avgReturn": 0.00023408507818968425, "sharpeRatio": 0.0036007651309708693, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5521893047308628, "confidence": 0.39, "performance": {"winRate": 0.6, "avgReturn": 0.005050321023918875, "sharpeRatio": 0.08349527962684916, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.19926738717664091, "confidence": 0.42000000000000004, "performance": {"winRate": 0.1, "avgReturn": -0.0432326128233591, "sharpeRatio": -1.1020530280584895, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.17589391505977253, "lastUpdated": 1749829450977}, "B3USDT": {"symbol": "B3USDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.63181491234983, "confidence": 0.41171428571428575, "performance": {"winRate": 0.7142857142857143, "avgReturn": 0.016632922959051805, "sharpeRatio": 0.24893292716785767, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.32045012073914625, "confidence": 0.4636551724137931, "performance": {"winRate": 0.27586206896551724, "avgReturn": -0.01735039559987417, "sharpeRatio": -0.4740824505814652, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5925838379623176, "confidence": 0.42200000000000004, "performance": {"winRate": 0.68, "avgReturn": 0.007649725161099325, "sharpeRatio": 0.1707726395628824, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.209693990235793, "lastUpdated": 1749829450977}, "1000BTTUSDT": {"symbol": "1000BTTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6266556843304802, "confidence": 0.4410909090909091, "performance": {"winRate": 0.7727272727272727, "avgReturn": 0.005685674341039893, "sharpeRatio": 0.2626191110783225, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.44904785606833253, "confidence": 0.3990370370370371, "performance": {"winRate": 0.4074074074074074, "avgReturn": -0.001962599776425129, "sharpeRatio": -0.13468290278314127, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5703195648941456, "confidence": 0.4047272727272727, "performance": {"winRate": 0.6818181818181818, "avgReturn": 0.0019412333362607177, "sharpeRatio": 0.11759240400679703, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.21660568338606773, "lastUpdated": 1749829450977}, "1000RATSUSDT": {"symbol": "1000RATSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5256022226196533, "confidence": 0.37733333333333335, "performance": {"winRate": 0.5833333333333334, "avgReturn": 0.0009394002543068703, "sharpeRatio": 0.015611250079848272, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4414021316657068, "confidence": 0.39, "performance": {"winRate": 0.4, "avgReturn": -0.006265861249209895, "sharpeRatio": -0.10842929504631829, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5606270253674789, "confidence": 0.4175384615384615, "performance": {"winRate": 0.6538461538461539, "avgReturn": 0.0038631936131258747, "sharpeRatio": 0.07046105906689876, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19613935392173232, "lastUpdated": 1749829450977}, "GIGAUSDT": {"symbol": "GIGAUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.7046916911460217, "confidence": 0.4483076923076923, "performance": {"winRate": 0.7307692307692307, "avgReturn": 0.030801993261247076, "sharpeRatio": 0.5559689733330961, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4658811120055637, "confidence": 0.374, "performance": {"winRate": 0.44, "avgReturn": -0.0037939152831062634, "sharpeRatio": -0.057032961274281875, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6069786451343232, "confidence": 0.4138518518518519, "performance": {"winRate": 0.6296296296296297, "avgReturn": 0.013297548538428672, "sharpeRatio": 0.22375107575754613, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.23033936951488165, "lastUpdated": 1749829450977}, "MYRIAUSDT": {"symbol": "MYRIAUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5506109691964356, "confidence": 0.38147826086956527, "performance": {"winRate": 0.6086956521739131, "avgReturn": 0.005690045433101182, "sharpeRatio": 0.05994825100630767, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.5931714003905234, "confidence": 0.3606666666666667, "performance": {"winRate": 0.4583333333333333, "avgReturn": 0.028043304549220303, "sharpeRatio": 0.2661997633917419, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.49715254376623014, "confidence": 0.38147826086956527, "performance": {"winRate": 0.6086956521739131, "avgReturn": -0.006454529675763484, "sharpeRatio": -0.08934081780528079, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.5512564561826756, "confidence": 0.3, "performance": {"winRate": 0.4, "avgReturn": 0.02947835686271446, "sharpeRatio": 0.21965933646784147, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19475327283080887, "lastUpdated": 1749829450977}, "DEGENUSDT": {"symbol": "DEGENUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5065359909310365, "confidence": 0.35018181818181815, "performance": {"winRate": 0.5454545454545454, "avgReturn": -0.0008730476114461348, "sharpeRatio": -0.01736957643234847, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.37252511441643554, "confidence": 0.48277419354838713, "performance": {"winRate": 0.25806451612903225, "avgReturn": -0.010255760675922149, "sharpeRatio": -0.22368659843391753, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6059253569421745, "confidence": 0.41171428571428575, "performance": {"winRate": 0.7142857142857143, "avgReturn": 0.010130411118310253, "sharpeRatio": 0.16406634786125696, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19667333287432393, "lastUpdated": 1749829450977}, "JASMYUSDT": {"symbol": "JASMYUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6530787974516067, "confidence": 0.4336521739130435, "performance": {"winRate": 0.7391304347826086, "avgReturn": 0.010969018740789604, "sharpeRatio": 0.405059147766533, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4047000341832999, "confidence": 0.4360689655172414, "performance": {"winRate": 0.3448275862068966, "avgReturn": -0.006793718800227203, "sharpeRatio": -0.2282082760058173, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5057146565821848, "confidence": 0.3606666666666667, "performance": {"winRate": 0.5416666666666666, "avgReturn": -0.0005644230066859757, "sharpeRatio": -0.019161405555971508, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20552014624412096, "lastUpdated": 1749829450977}, "HOTUSDT": {"symbol": "HOTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.590763204568793, "confidence": 0.3988695652173913, "performance": {"winRate": 0.6521739130434783, "avgReturn": 0.007315299546479144, "sharpeRatio": 0.2100678225280948, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.41117129961627114, "confidence": 0.43333333333333335, "performance": {"winRate": 0.36666666666666664, "avgReturn": -0.0052111158237150654, "sharpeRatio": -0.2503612165952539, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6040001086941647, "confidence": 0.42200000000000004, "performance": {"winRate": 0.68, "avgReturn": 0.006378143071531834, "sharpeRatio": 0.24419664439834776, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.21217493456386055, "lastUpdated": 1749829450977}, "FARTCOINUSDT": {"symbol": "FARTCOINUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6035658443578797, "confidence": 0.44000000000000006, "performance": {"winRate": 0.8, "avgReturn": 0.007283095925374947, "sharpeRatio": 0.07970854996927187, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4369406372638408, "confidence": 0.4360689655172414, "performance": {"winRate": 0.3448275862068966, "avgReturn": -0.004600061276293495, "sharpeRatio": -0.06779269241546478, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.4760555810737745, "confidence": 0.4084827586206896, "performance": {"winRate": 0.5862068965517241, "avgReturn": -0.008655718798965798, "sharpeRatio": -0.12313425556638974, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20764143005383628, "lastUpdated": 1749829450977}, "ENAUSDT": {"symbol": "ENAUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5483941846517466, "confidence": 0.37466666666666665, "performance": {"winRate": 0.6666666666666666, "avgReturn": 0.0016931749130690173, "sharpeRatio": 0.02251473485225717, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4847755921593741, "confidence": 0.38676923076923075, "performance": {"winRate": 0.4230769230769231, "avgReturn": 0.0005900143609369786, "sharpeRatio": 0.014398741653354617, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5200968333001348, "confidence": 0.4175384615384615, "performance": {"winRate": 0.6538461538461539, "avgReturn": -0.003164200638119942, "sharpeRatio": -0.06295587016830856, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19753043392129943, "lastUpdated": 1749829450977}, "IOSTUSDT": {"symbol": "IOSTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.48612226320561214, "confidence": 0.34, "performance": {"winRate": 0.55, "avgReturn": -0.004033177345189441, "sharpeRatio": -0.1133907849487222, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.5729063827774963, "confidence": 0.36408695652173917, "performance": {"winRate": 0.5652173913043478, "avgReturn": 0.006681985937437783, "sharpeRatio": 0.23134319745370632, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5386244544110358, "confidence": 0.406, "performance": {"winRate": 0.64, "avgReturn": 0.0004394996922221836, "sharpeRatio": 0.015274238734217616, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.19313770981453143, "lastUpdated": 1749829450977}, "GORKUSDT": {"symbol": "GORKUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5613288237124961, "confidence": 0.346, "performance": {"winRate": 0.625, "avgReturn": 0.009566612606057014, "sharpeRatio": 0.08732071167235776, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.6627798791282814, "confidence": 0.3694074074074074, "performance": {"winRate": 0.5185185185185185, "avgReturn": 0.036178955294656985, "sharpeRatio": 0.5356051876752864, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.4895361714332864, "confidence": 0.3990370370370371, "performance": {"winRate": 0.5925925925925926, "avgReturn": -0.006752148164025939, "sharpeRatio": -0.09435465687474864, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20359965830157262, "lastUpdated": 1749829450977}, "AI16ZUSDT": {"symbol": "AI16ZUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5255283205469066, "confidence": 0.39266666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": -0.003994525138871701, "sharpeRatio": -0.04505963905811353, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.37889413223818647, "confidence": 0.4175384615384615, "performance": {"winRate": 0.34615384615384615, "avgReturn": -0.014553955798716703, "sharpeRatio": -0.2801144177012518, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.49808564086657103, "confidence": 0.4106666666666666, "performance": {"winRate": 0.6666666666666666, "avgReturn": -0.009448274117530438, "sharpeRatio": -0.12441158652776545, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.1872768741944933, "lastUpdated": 1749829450977}, "OBTUSDT": {"symbol": "OBTUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6676592768136647, "confidence": 0.4410909090909091, "performance": {"winRate": 0.7727272727272727, "avgReturn": 0.02337158240430195, "sharpeRatio": 0.2750961380151348, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.35171941764779746, "confidence": 0.43799999999999994, "performance": {"winRate": 0.28, "avgReturn": -0.015275526860404391, "sharpeRatio": -0.35154088339604217, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.7143653117310436, "confidence": 0.4582962962962963, "performance": {"winRate": 0.7407407407407407, "avgReturn": 0.027800208630030528, "sharpeRatio": 0.5347647426692141, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.49028370651669145, "confidence": 0.33999999999999997, "performance": {"winRate": 0.3, "avgReturn": 0.007747191634956255, "sharpeRatio": 0.15260840864143171, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.23565974477175378, "lastUpdated": 1749829450977}, "SKATEUSDT": {"symbol": "SKATEUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.7, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.18000000000000002, "lastUpdated": 1749829450977}, "MOODENGUSDT": {"symbol": "MOODENGUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.5918205776040004, "confidence": 0.39999999999999997, "performance": {"winRate": 0.7, "avgReturn": 0.010477184247398946, "sharpeRatio": 0.12202783948087077, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.45114818686588676, "confidence": 0.4483076923076923, "performance": {"winRate": 0.2692307692307692, "avgReturn": 0.0020215986298675673, "sharpeRatio": 0.020857690492054787, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.5000734324438252, "confidence": 0.394, "performance": {"winRate": 0.625, "avgReturn": -0.006452728165335746, "sharpeRatio": -0.09410395913677948, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.20400259149177813, "lastUpdated": 1749829450977}, "XEMUSDT": {"symbol": "XEMUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.41174844194888005, "confidence": 0.31376470588235295, "performance": {"winRate": 0.5294117647058824, "avgReturn": -0.02988591630365271, "sharpeRatio": -0.2779424528997409, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.33129056875602453, "confidence": 0.502, "performance": {"winRate": 0.12, "avgReturn": -0.016504090877677912, "sharpeRatio": -0.2004778039857245, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.3725234227560128, "confidence": 0.35018181818181815, "performance": {"winRate": 0.45454545454545453, "avgReturn": -0.02914419510530752, "sharpeRatio": -0.4199528385767911, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.15148773094928658, "lastUpdated": 1749829450977}, "1000CATSUSDT": {"symbol": "1000CATSUSDT", "indicators": {"RSI": {"indicator": "RSI", "weight": 0.6681934549790175, "confidence": 0.44400000000000006, "performance": {"winRate": 0.75, "avgReturn": 0.01684817519828244, "sharpeRatio": 0.3845260926830939, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MACD": {"indicator": "MACD", "weight": 0.4923451298424183, "confidence": 0.3773333333333333, "performance": {"winRate": 0.4166666666666667, "avgReturn": 0.002167928023851129, "sharpeRatio": 0.04493338823056568, "consistency": 0.5}, "lastUpdated": 1749829450977}, "BollingerBands": {"indicator": "BollingerBands", "weight": 0.6430445741264842, "confidence": 0.43292307692307697, "performance": {"winRate": 0.6923076923076923, "avgReturn": 0.01462721680331616, "sharpeRatio": 0.32479718869332447, "consistency": 0.5}, "lastUpdated": 1749829450977}, "MovingAverage": {"indicator": "MovingAverage", "weight": 0.6, "confidence": 0.3, "performance": {"winRate": 0.5, "avgReturn": 0, "sharpeRatio": 0, "consistency": 0.5}, "lastUpdated": 1749829450977}}, "overallScore": 0.23521123965852087, "lastUpdated": 1749829450977}}, "correlations": {"matrix": {"symbols": ["1000PEPEUSDT", "DOGSUSDT", "HMSTRUSDT", "10000SATSUSDT", "NOTUSDT", "BOMEUSDT", "1000BONKUSDT", "GALAUSDT", "MEWUSDT", "ANIMEUSDT", "RVNUSDT", "SHIB1000USDT", "PENGUUSDT", "DOGEUSDT", "MOBILEUSDT", "XVGUSDT", "MEMEUSDT", "ZBCNUSDT", "10000WHYUSDT", "SPELLUSDT", "ORBSUSDT", "10000ELONUSDT", "HIPPOUSDT", "10000LADYSUSDT", "ZEREBROUSDT", "DENTUSDT", "LEVERUSDT", "1000000BABYDOGEUSDT", "DOGUSDT", "PEOPLEUSDT", "BRETTUSDT", "B3USDT", "1000BTTUSDT", "1000RATSUSDT", "GIGAUSDT", "MYRIAUSDT", "DEGENUSDT", "JASMYUSDT", "HOTUSDT", "FARTCOINUSDT", "ENAUSDT", "IOSTUSDT", "GORKUSDT", "AI16ZUSDT", "OBTUSDT", "SKATEUSDT", "MOODENGUSDT", "XEMUSDT", "1000CATSUSDT"], "matrix": [[1, 0.8479879844281345, 0.3390498665172877, 0.6794415451076605, 0.8224912283143854, 0.8514631116718933, 0.8772615684153436, 0.8622976356629619, 0.751870767425692, 0.3165871920124114, 0.23335753575287238, 0.8993850435935925, 0.744439896583286, 0.8737997152678266, 0.5580234052728479, 0.7369848753475231, 0.8523986846157423, 0.22778082831822807, 0.5344779935320361, 0.6687290256377548, 0.4045339800154209, 0.7316057803846574, 0.561505513969027, 0.7692108510361214, 0.5585096123216865, 0.8436372817282779, 0.7297108343082662, 0.865965881210877, 0.5829790415363104, 0.7576893613343423, 0.8104932903685567, 0.5924104356767549, 0.6816160142249588, 0.4556037931990281, 0.6518783345022898, 0.46371351398482796, 0.6576962805803057, 0.8150954307689653, 0.8109869655801457, 0.6846377266429023, 0.7969811315720045, 0.6164524272997818, 0.4670753099888584, 0.7966439025869176, 0.23451515621052663, 0.4130030646450816, 0.6469357170692362, 0.35844487920597207, 0.4056244316315685], [0.8479879844281345, 1, 0.39403786051361955, 0.7640160209965847, 0.8959432946648643, 0.8973607650009137, 0.853093369119388, 0.8815492327899662, 0.772987300744936, 0.36450801400904076, 0.2721593429620069, 0.8765094424738777, 0.7370652072732814, 0.844269476511372, 0.5371457724553497, 0.7346118901900959, 0.9139815932086011, 0.22561543983477236, 0.5824003718608369, 0.686739334043319, 0.4113781009169654, 0.7059658315150102, 0.5940578335666702, 0.7648547840259383, 0.5987644183109823, 0.8499866068196686, 0.7690096246532163, 0.8930033037058612, 0.635408354759048, 0.797492750862137, 0.7997242457898974, 0.5626139087663719, 0.6776513314151874, 0.527706047588997, 0.6237232896883227, 0.45520775498570654, 0.6786422695133907, 0.8186911086404077, 0.827903969070813, 0.6618561796713425, 0.7809173897573533, 0.6345719488279706, 0.49251824381711545, 0.8160383702338717, 0.21717993600790655, 0.40626038923564417, 0.689805265092909, 0.34424009451656745, 0.425408780407193], [0.3390498665172877, 0.39403786051361955, 1, 0.34040555732804767, 0.38654316669822514, 0.3594653918924726, 0.3455432821450257, 0.36675788426704503, 0.3101131950467346, 0.16493510893143742, 0.0766903561645847, 0.3403029594634556, 0.30514514686265776, 0.32207692472665633, 0.21810938263069649, 0.3424761561871678, 0.3743540838865748, 0.12681984077634761, 0.2655590888288259, 0.3046913471157823, 0.1506705555379408, 0.29608652499314553, 0.2689018109921721, 0.3469941037011045, 0.25396835579402227, 0.3633161542266615, 0.3288948923991439, 0.365455819720716, 0.2589888013303253, 0.332561793120634, 0.3256271004277091, 0.2670143947666841, 0.28833004125496925, 0.20144410134945678, 0.2578670783696155, 0.21270779883264945, 0.29776376063893745, 0.3649511103116572, 0.3586410363568399, 0.2715249783660158, 0.30123441966484593, 0.2966761836518211, 0.1582661861154594, 0.3257589607475293, 0.11199141087285773, 0.32127863798559225, 0.27918559621778355, 0.12391380561803007, 0.18171325412820918], [0.6794415451076605, 0.7640160209965847, 0.34040555732804767, 1, 0.7405714799162353, 0.7561967200515034, 0.6813463720881726, 0.736075815479241, 0.6240426835859223, 0.31901458676867406, 0.2165469908672692, 0.7274722293651218, 0.6216865794821311, 0.7037214571947287, 0.43445066351858547, 0.6465174672145827, 0.7629913551815589, 0.2258553909969479, 0.5359030962699258, 0.5592389948406252, 0.36299805498397375, 0.5551816952249239, 0.4747805447595086, 0.6235438287098809, 0.5329489110855089, 0.7038777017341687, 0.6501587878256362, 0.7478745400722095, 0.5356986247986407, 0.7023789202059109, 0.6644295171444999, 0.4822869632219892, 0.5877635433407234, 0.5243022182523336, 0.5188432058986974, 0.3595915534615474, 0.5735867348627027, 0.6977724211430392, 0.694540972022311, 0.5155595682961401, 0.6507457617293304, 0.5331247331709942, 0.3927734330511016, 0.6822812496517481, 0.19337954931554058, 0.39241797222610564, 0.5382972744427756, 0.2596932426072543, 0.3880110797731558], [0.8224912283143854, 0.8959432946648643, 0.38654316669822514, 0.7405714799162353, 1, 0.8608658467300271, 0.8152953644245504, 0.8752034911797844, 0.7427320224795475, 0.34107857812971204, 0.2688091086337159, 0.8538818314616711, 0.7236664431957419, 0.8206527595929635, 0.5286974631110863, 0.7319883611493909, 0.8905094131448607, 0.19588657064295095, 0.5980464952604465, 0.6597324622977014, 0.4157880474951049, 0.6908668321790764, 0.5656783072961116, 0.7504734512480256, 0.5803228882280766, 0.8496107575490839, 0.753066492811592, 0.8643867108608456, 0.6024015484541502, 0.7924211032148927, 0.7812130356223989, 0.5559549199646076, 0.6623569945510271, 0.5071839269412645, 0.6147280864835156, 0.45034230412284454, 0.6451528004857084, 0.80131454200536, 0.8238848040997583, 0.6428493250516379, 0.7684366225693503, 0.6240312651962905, 0.5002243575995712, 0.7823880680776243, 0.23103563682280512, 0.41187406673705046, 0.6346171468542742, 0.3339229303899729, 0.4419887233572881], [0.8514631116718933, 0.8973607650009137, 0.3594653918924726, 0.7561967200515034, 0.8608658467300271, 1, 0.864400778495709, 0.8716716811174146, 0.7839674205236209, 0.339509666121446, 0.2573638542259473, 0.8758931490214702, 0.7439037979728003, 0.844446552517996, 0.5379166090070379, 0.7215263499150021, 0.9138187139527716, 0.21438631870896804, 0.5714904831880715, 0.6615804301890134, 0.4181908259318669, 0.6956149875475885, 0.6099381711504304, 0.7660826929604837, 0.6102455378183369, 0.8291584186462694, 0.7435551335247068, 0.8866385097800491, 0.6080502432042149, 0.8313996370844094, 0.8010398316073291, 0.5568242945686606, 0.6599513344723986, 0.5213366072975633, 0.6241810076893011, 0.44962275895111947, 0.6974478687317146, 0.8076732342577193, 0.8110420584836157, 0.6536710843482162, 0.770174381123015, 0.6294991523793647, 0.4675453654304379, 0.8136502626044819, 0.22314689389548853, 0.4390030094456399, 0.725614666792474, 0.347775525492682, 0.4300509108158493], [0.8772615684153436, 0.853093369119388, 0.3455432821450257, 0.6813463720881726, 0.8152953644245504, 0.864400778495709, 1, 0.873564803898303, 0.7654999213131789, 0.338051324114667, 0.25453492296047026, 0.8902705344894797, 0.757266311275353, 0.8680284135462111, 0.5766558672212126, 0.7430378446498223, 0.8594055868209889, 0.22223106271771823, 0.5620608140596436, 0.6837782093186955, 0.3817307984549282, 0.7143664384305976, 0.5632579449646227, 0.7624745299644766, 0.5783451238506634, 0.8398113118603237, 0.7372570311059526, 0.8681099957875211, 0.6188583481051368, 0.7490532091300146, 0.7914241621671899, 0.5650849926206424, 0.6760441811594454, 0.44007483722961666, 0.6589491147119237, 0.478137420471331, 0.6538722758986445, 0.8264950090991093, 0.8100665012361495, 0.7121517240572611, 0.7804761716147748, 0.6212680419735105, 0.46373574540640156, 0.8214184131522386, 0.22453103657939336, 0.4010595866534586, 0.6723918991673666, 0.3701528763373425, 0.419952318707894], [0.8622976356629619, 0.8815492327899662, 0.36675788426704503, 0.736075815479241, 0.8752034911797844, 0.8716716811174146, 0.873564803898303, 1, 0.7656585352629232, 0.35008358377848803, 0.28641016635281985, 0.914764842013035, 0.7531467098216579, 0.8840071796301877, 0.5697275901158751, 0.7718246525992802, 0.9008912799097804, 0.21926796043090765, 0.5902699256194106, 0.7062861987915158, 0.4379028733945269, 0.7373961627760982, 0.5646091000965723, 0.7979016003637044, 0.5695518975298192, 0.8941494172168207, 0.7875245572215656, 0.8812231581356895, 0.6225380661876514, 0.7734690685488457, 0.8274812489399561, 0.5975493831201029, 0.7131147812464499, 0.4890450433133643, 0.6509070364702314, 0.47426150848685483, 0.6576927496521051, 0.8562977219607063, 0.8780202335409746, 0.693068488431548, 0.8340465456099113, 0.6635101052730389, 0.4759491482096237, 0.8258914117471309, 0.2130012738893111, 0.39498542006052484, 0.6298922731758342, 0.36626712383654, 0.47388974398249856], [0.751870767425692, 0.772987300744936, 0.3101131950467346, 0.6240426835859223, 0.7427320224795475, 0.7839674205236209, 0.7654999213131789, 0.7656585352629232, 1, 0.31079133984584667, 0.2205873237159412, 0.7741515149879258, 0.6474738373098505, 0.7559906575166739, 0.49241791162501863, 0.6475219035489636, 0.7944839943396419, 0.18676729422647118, 0.5151068130792124, 0.6089293362411135, 0.3666021736257105, 0.612861536871722, 0.5228672408894416, 0.6597510835222015, 0.541681051935789, 0.7397355419030525, 0.664467487114323, 0.7811867441222988, 0.5897115680362807, 0.7023801122326925, 0.6955346205246886, 0.49800251168245696, 0.5884464022700008, 0.44864064901229794, 0.5603400581056495, 0.4237054846919552, 0.5890277721714995, 0.7220631620922536, 0.7295900381814762, 0.6230991049087516, 0.6929913023855454, 0.5556188155155783, 0.40443394633419943, 0.7370779927994218, 0.2001477476738158, 0.43680893522446124, 0.6672511838723032, 0.3302107758724547, 0.3967707723979441], [0.3165871920124114, 0.36450801400904076, 0.16493510893143742, 0.31901458676867406, 0.34107857812971204, 0.339509666121446, 0.338051324114667, 0.35008358377848803, 0.31079133984584667, 1, 0.09298018724852544, 0.3576142452351982, 0.2656462740426977, 0.3392253753246435, 0.20908939651349417, 0.3183388459115234, 0.3588650058461815, 0.09864920415099025, 0.28668743961386817, 0.27348506473639417, 0.14454612709621162, 0.25854215785207674, 0.24827850680831282, 0.30202897187305017, 0.24364405104686343, 0.3780098817983473, 0.3422443617510079, 0.3605333020980218, 0.25437654913238533, 0.3290693678600206, 0.2826658376490538, 0.22683366384961168, 0.28924962794204456, 0.20412050010021401, 0.26694833992069644, 0.20457454747958523, 0.23753595412171713, 0.3506740698827862, 0.3526040997249608, 0.2858241859256449, 0.28083864064049735, 0.3046580608609465, 0.23897228240210772, 0.34089587108913627, 0.11067086413161949, 0.24384504629252815, 0.2740136955126952, 0.1664348377989984, 0.20255763646023311], [0.23335753575287238, 0.2721593429620069, 0.0766903561645847, 0.2165469908672692, 0.2688091086337159, 0.2573638542259473, 0.25453492296047026, 0.28641016635281985, 0.2205873237159412, 0.09298018724852544, 1, 0.26843686279698564, 0.21945093571106886, 0.23219087119633353, 0.13159396292098818, 0.27065503192509155, 0.275601513102909, 0.012019342538048913, 0.17005574930340572, 0.23236038030561737, 0.10760589250062956, 0.23943411135330495, 0.16177386746283118, 0.22846374550107423, 0.12259529675315516, 0.30948395445611543, 0.2611617325429528, 0.27060015151595235, 0.17858224336483322, 0.2307726931673192, 0.2550578695507802, 0.19174862376788773, 0.22097542997372682, 0.1662286958048068, 0.1842493734925467, 0.17999861919065466, 0.18446660561227274, 0.24167793865974685, 0.4875636665448887, 0.2568621395874813, 0.2482137248945712, 0.23512718998229787, 0.14086358371805355, 0.251563266856023, -0.03651107994640663, 0.03044538214810526, 0.16625955869782147, 0.09900651909531864, 0.108907576618161], [0.8993850435935925, 0.8765094424738777, 0.3403029594634556, 0.7274722293651218, 0.8538818314616711, 0.8758931490214702, 0.8902705344894797, 0.914764842013035, 0.7741515149879258, 0.3576142452351982, 0.26843686279698564, 1, 0.7499544528263583, 0.9316320483734198, 0.5710278624545998, 0.7607921889997932, 0.8943471781355498, 0.21313080812478685, 0.5823294574173576, 0.6987443390005594, 0.42874802857235855, 0.7726998786900565, 0.5626718943185806, 0.7994337697639412, 0.5763070487794583, 0.8844946771427824, 0.7669780297686747, 0.8970353719874615, 0.62504084169176, 0.7647310504647976, 0.8247053920287583, 0.5811810118735568, 0.7275115576480358, 0.49219904348282245, 0.6536137385284132, 0.47912032302484037, 0.672170427518673, 0.8547364483935744, 0.8663498839140469, 0.6995993981020716, 0.8193645689431447, 0.6663789932573703, 0.4779969208854028, 0.8229143111875248, 0.2430755452210987, 0.39762502869543126, 0.6466940692464547, 0.36843983767805766, 0.44497799905571295], [0.744439896583286, 0.7370652072732814, 0.30514514686265776, 0.6216865794821311, 0.7236664431957419, 0.7439037979728003, 0.757266311275353, 0.7531467098216579, 0.6474738373098505, 0.2656462740426977, 0.21945093571106886, 0.7499544528263583, 1, 0.7198161900434711, 0.5268660142386151, 0.6386846098095423, 0.7639695723538676, 0.19487287349911356, 0.4725626264688002, 0.5928775898897272, 0.31151474110598454, 0.6020966691166852, 0.47785970551257123, 0.6418675646921448, 0.5493751937727719, 0.7444448153733111, 0.661813879440015, 0.7417114407692309, 0.5572831738639601, 0.6467140918636151, 0.707613138963918, 0.5230057763317011, 0.5874635220315619, 0.4232079430798325, 0.5521370717684706, 0.39448025568567086, 0.5921106891703877, 0.7018961685907997, 0.6960845288191622, 0.6052258484998115, 0.6603792331998247, 0.5714560490214057, 0.43404994711800343, 0.7440230454067485, 0.22755400335266654, 0.3277274477067024, 0.571495425557669, 0.33248842306752613, 0.39548087031948936], [0.8737997152678266, 0.844269476511372, 0.32207692472665633, 0.7037214571947287, 0.8206527595929635, 0.844446552517996, 0.8680284135462111, 0.8840071796301877, 0.7559906575166739, 0.3392253753246435, 0.23219087119633353, 0.9316320483734198, 0.7198161900434711, 1, 0.5459492891568256, 0.7380488402602603, 0.8532866126472013, 0.21182844808756948, 0.5806223901301671, 0.6899279113049216, 0.41953918514982136, 0.7587017500213775, 0.5416357869424505, 0.7683329062175516, 0.5506480402403331, 0.8592209603919319, 0.7457461358676601, 0.8733012229238581, 0.6315751681804079, 0.7453497280160792, 0.7862720297870102, 0.5750037646106311, 0.7039791835687518, 0.46544747858108293, 0.6283903644832871, 0.48409934369954033, 0.6436993897363721, 0.8249613658558704, 0.8292104240662349, 0.6811006292997583, 0.7889779950354513, 0.6334889266278599, 0.4501315325436523, 0.7980935366135766, 0.24833669644856074, 0.3772528257642741, 0.6069535300195092, 0.3715895516108034, 0.4577960410256718], [0.5580234052728479, 0.5371457724553497, 0.21810938263069649, 0.43445066351858547, 0.5286974631110863, 0.5379166090070379, 0.5766558672212126, 0.5697275901158751, 0.49241791162501863, 0.20908939651349417, 0.13159396292098818, 0.5710278624545998, 0.5268660142386151, 0.5459492891568256, 1, 0.477493112852461, 0.5632238599188665, 0.11048024539573419, 0.36611599421382984, 0.46987817158082423, 0.3048414529099803, 0.46360332719957176, 0.2940992516738208, 0.5184008502877644, 0.40316356256895275, 0.5375433047933381, 0.48566103972584646, 0.5566987138670147, 0.4079221366530104, 0.46981405435516094, 0.5294858668698871, 0.3576100406413816, 0.4913959890791104, 0.32492001738207893, 0.40316128021244596, 0.3039403131600632, 0.44686230225299983, 0.5626745826565899, 0.5389000162059105, 0.4857250228725273, 0.5150888151321936, 0.4355431183360689, 0.2864779694383888, 0.5225147935222171, 0.1612015780320931, 0.22316331318324528, 0.4029385227667962, 0.21713823705072025, 0.3134836037557546], [0.7369848753475231, 0.7346118901900959, 0.3424761561871678, 0.6465174672145827, 0.7319883611493909, 0.7215263499150021, 0.7430378446498223, 0.7718246525992802, 0.6475219035489636, 0.3183388459115234, 0.27065503192509155, 0.7607921889997932, 0.6386846098095423, 0.7380488402602603, 0.477493112852461, 1, 0.7551293687329114, 0.2156000828032046, 0.4923561323283689, 0.6495940813784647, 0.3489646705088611, 0.6295265922643137, 0.5060692437772928, 0.6770996226034413, 0.49692120853886396, 0.7698067919562227, 0.6696918816619033, 0.7489843498535167, 0.5062611497760164, 0.6744183214430425, 0.6861057554031478, 0.5388372689574177, 0.5924475124439436, 0.47060965370130486, 0.5743393625526828, 0.42806173126495795, 0.5783646125463775, 0.7572175699351008, 0.7375174386159399, 0.5798297155271349, 0.6848034340396245, 0.5615089730180831, 0.38680320307617355, 0.6990380361048294, 0.2246681035142802, 0.4012923258022414, 0.5566747565996849, 0.32085815613585034, 0.3584950444078775], [0.8523986846157423, 0.9139815932086011, 0.3743540838865748, 0.7629913551815589, 0.8905094131448607, 0.9138187139527716, 0.8594055868209889, 0.9008912799097804, 0.7944839943396419, 0.3588650058461815, 0.275601513102909, 0.8943471781355498, 0.7639695723538676, 0.8532866126472013, 0.5632238599188665, 0.7551293687329114, 1, 0.21744641679047974, 0.5941592449688691, 0.7120657076022193, 0.44378675123558664, 0.7189961252882027, 0.6052830577122549, 0.7825292941838256, 0.6076398996767505, 0.8693361014007351, 0.773162295591577, 0.9024162317782997, 0.6181574699432231, 0.8103423065296728, 0.8181902055079902, 0.5837370585124132, 0.6954999452070106, 0.5570227623133962, 0.6269900759298679, 0.4740069596656412, 0.6945604245680249, 0.833108513880794, 0.8484487731746689, 0.656008154267582, 0.799559063000362, 0.6681651593313045, 0.4836813186963104, 0.8218247818272447, 0.24345276929175338, 0.40001083087961803, 0.6858684069406581, 0.362087644267486, 0.4502775292129621], [0.22778082831822807, 0.22561543983477236, 0.12681984077634761, 0.2258553909969479, 0.19588657064295095, 0.21438631870896804, 0.22223106271771823, 0.21926796043090765, 0.18676729422647118, 0.09864920415099025, 0.012019342538048913, 0.21313080812478685, 0.19487287349911356, 0.21182844808756948, 0.11048024539573419, 0.2156000828032046, 0.21744641679047974, 1, 0.18067975216224866, 0.18702601097613572, 0.10231088423779072, 0.22532675785893072, 0.10016843059740224, 0.19310015589827273, 0.203636054557841, 0.20011371905043612, 0.18409189627364958, 0.22408153104444425, 0.16022406306191422, 0.20949273926649445, 0.2202084589850863, 0.202711436373638, 0.17850956156743816, 0.10887215907883849, 0.20738866094430403, 0.11099243637855767, 0.17369007266940464, 0.20171731832289078, 0.20216456107642622, 0.17433976780740243, 0.19661614589730428, 0.16074902358662746, 0.1633412846930407, 0.22564522532888565, 0.10828153052069799, 0.33062721514772686, 0.16087105202912097, 0.055940453092359066, 0.06554241260394056], [0.5344779935320361, 0.5824003718608369, 0.2655590888288259, 0.5359030962699258, 0.5980464952604465, 0.5714904831880715, 0.5620608140596436, 0.5902699256194106, 0.5151068130792124, 0.28668743961386817, 0.17005574930340572, 0.5823294574173576, 0.4725626264688002, 0.5806223901301671, 0.36611599421382984, 0.4923561323283689, 0.5941592449688691, 0.18067975216224866, 1, 0.4701593060881887, 0.29530913084766874, 0.4898730642395298, 0.4053040145262872, 0.537074295465751, 0.4286130768626484, 0.5683360504867085, 0.517710330082575, 0.5991690590309057, 0.4754848039878175, 0.5096501215604857, 0.5485905233571204, 0.425927647599659, 0.425097407795924, 0.36910894731982286, 0.43062746921102163, 0.32005572359187967, 0.48997895719032897, 0.5751060006833564, 0.5550726456867593, 0.4244993270400992, 0.5296933550779928, 0.4384385563581909, 0.35910088165154774, 0.5375892886137237, 0.21549876563406958, 0.3671075234426417, 0.4299311085473435, 0.2538188771425222, 0.31157768573560224], [0.6687290256377548, 0.686739334043319, 0.3046913471157823, 0.5592389948406252, 0.6597324622977014, 0.6615804301890134, 0.6837782093186955, 0.7062861987915158, 0.6089293362411135, 0.27348506473639417, 0.23236038030561737, 0.6987443390005594, 0.5928775898897272, 0.6899279113049216, 0.46987817158082423, 0.6495940813784647, 0.7120657076022193, 0.18702601097613572, 0.4701593060881887, 1, 0.3421184084052451, 0.6013381994857138, 0.4846941532320609, 0.6160852457887507, 0.45373797526642395, 0.7117066045710376, 0.6183729229882693, 0.7031635861677678, 0.46266573482408696, 0.6032967514065071, 0.6457006464767121, 0.44498319759933874, 0.5606958648398861, 0.40237339219216384, 0.47125162581303653, 0.3898708420311344, 0.5321172950897487, 0.6999830193350762, 0.6717199919346304, 0.5447789050377078, 0.6107361601169621, 0.5327869493482574, 0.3525447777165428, 0.6389086322279299, 0.15182035728285984, 0.37159728306170514, 0.508380828926691, 0.3008135779553687, 0.38532953064119274], [0.4045339800154209, 0.4113781009169654, 0.1506705555379408, 0.36299805498397375, 0.4157880474951049, 0.4181908259318669, 0.3817307984549282, 0.4379028733945269, 0.3666021736257105, 0.14454612709621162, 0.10760589250062956, 0.42874802857235855, 0.31151474110598454, 0.41953918514982136, 0.3048414529099803, 0.3489646705088611, 0.44378675123558664, 0.10231088423779072, 0.29530913084766874, 0.3421184084052451, 1, 0.3607150375012218, 0.2819003614780518, 0.38187368622191503, 0.259865280134744, 0.3306013222697761, 0.40307545992438654, 0.4530878567785834, 0.2364061117243543, 0.35815369539393493, 0.38956068908077074, 0.2437641223258339, 0.39116334984354867, 0.45082104754473573, 0.2089182059148753, 0.26399569678983803, 0.3300730611918849, 0.3970253256964555, 0.42734319607512067, 0.29133258872205753, 0.3859657593247586, 0.3735377857889201, 0.2055386293001575, 0.40167485041962125, 0.0799760309823122, -0.24928183227492326, 0.319064235843815, 0.13964352435370692, 0.2519193624214589], [0.7316057803846574, 0.7059658315150102, 0.29608652499314553, 0.5551816952249239, 0.6908668321790764, 0.6956149875475885, 0.7143664384305976, 0.7373961627760982, 0.612861536871722, 0.25854215785207674, 0.23943411135330495, 0.7726998786900565, 0.6020966691166852, 0.7587017500213775, 0.46360332719957176, 0.6295265922643137, 0.7189961252882027, 0.22532675785893072, 0.4898730642395298, 0.6013381994857138, 0.3607150375012218, 1, 0.43588908595824655, 0.6977652722577988, 0.4313133089709256, 0.740246314703937, 0.6306250032070329, 0.7372021088661385, 0.5249135380829218, 0.5815793468551865, 0.6769144268035497, 0.5119838508669989, 0.618937386264665, 0.35845977098072734, 0.5668969312120851, 0.4215365095421521, 0.551088045977826, 0.70997273298016, 0.7275551545841104, 0.5807974280720085, 0.6894576672319052, 0.560620008982558, 0.36818704563357446, 0.654492830044202, 0.20816739990921612, 0.2776981605532258, 0.4936382752948575, 0.3160528846940563, 0.3744953651159128], [0.561505513969027, 0.5940578335666702, 0.2689018109921721, 0.4747805447595086, 0.5656783072961116, 0.6099381711504304, 0.5632579449646227, 0.5646091000965723, 0.5228672408894416, 0.24827850680831282, 0.16177386746283118, 0.5626718943185806, 0.47785970551257123, 0.5416357869424505, 0.2940992516738208, 0.5060692437772928, 0.6052830577122549, 0.10016843059740224, 0.4053040145262872, 0.4846941532320609, 0.2819003614780518, 0.43588908595824655, 1, 0.48510239987444576, 0.4374789401553082, 0.55460008620593, 0.5099357130964174, 0.6092566481330048, 0.4089474842759252, 0.5687977106974668, 0.5215755355083198, 0.38186458562150905, 0.4099749649172086, 0.38219697394464347, 0.44513576956941897, 0.33510414733706567, 0.5023023684488164, 0.5472886864946094, 0.5389185753911498, 0.4463165738230815, 0.5046502034846476, 0.4152454888614595, 0.3251982325680836, 0.5338917659738438, 0.12290970835246504, 0.3862789728644773, 0.5369470367483534, 0.22261316133975165, 0.29953053030770926], [0.7692108510361214, 0.7648547840259383, 0.3469941037011045, 0.6235438287098809, 0.7504734512480256, 0.7660826929604837, 0.7624745299644766, 0.7979016003637044, 0.6597510835222015, 0.30202897187305017, 0.22846374550107423, 0.7994337697639412, 0.6418675646921448, 0.7683329062175516, 0.5184008502877644, 0.6770996226034413, 0.7825292941838256, 0.19310015589827273, 0.537074295465751, 0.6160852457887507, 0.38187368622191503, 0.6977652722577988, 0.48510239987444576, 1, 0.5112119677644854, 0.7698172018834595, 0.6968950205199295, 0.7759102095008025, 0.5486135955910426, 0.6460871119366836, 0.7303841685699854, 0.5708138989284883, 0.6504533911403361, 0.43995022903582537, 0.5845794669042784, 0.43533142844165995, 0.6205428017590721, 0.7325925266916579, 0.7571666638947978, 0.5950059328105081, 0.7212411061795921, 0.5734526314972103, 0.3994701550698036, 0.7177491740747094, 0.22922059956141888, 0.3281897460618858, 0.5466725400492003, 0.30079236553522803, 0.41304592751685315], [0.5585096123216865, 0.5987644183109823, 0.25396835579402227, 0.5329489110855089, 0.5803228882280766, 0.6102455378183369, 0.5783451238506634, 0.5695518975298192, 0.541681051935789, 0.24364405104686343, 0.12259529675315516, 0.5763070487794583, 0.5493751937727719, 0.5506480402403331, 0.40316356256895275, 0.49692120853886396, 0.6076398996767505, 0.203636054557841, 0.4286130768626484, 0.45373797526642395, 0.259865280134744, 0.4313133089709256, 0.4374789401553082, 0.5112119677644854, 1, 0.5505657709572485, 0.5135019248777569, 0.5984174151810282, 0.4649757690716436, 0.5292375159885326, 0.5533921956646682, 0.36742888264480683, 0.436068375159094, 0.39148537430689007, 0.4361381245187454, 0.31085030289017535, 0.5364000044895807, 0.5439427673715354, 0.5359603116439305, 0.4465075655278563, 0.5277503902925067, 0.4162161189835776, 0.33240361786336065, 0.594228100863131, 0.17792083178837373, 0.21370678426981402, 0.49975100670005396, 0.19522701104460569, 0.27134790511253315], [0.8436372817282779, 0.8499866068196686, 0.3633161542266615, 0.7038777017341687, 0.8496107575490839, 0.8291584186462694, 0.8398113118603237, 0.8941494172168207, 0.7397355419030525, 0.3780098817983473, 0.30948395445611543, 0.8844946771427824, 0.7444448153733111, 0.8592209603919319, 0.5375433047933381, 0.7698067919562227, 0.8693361014007351, 0.20011371905043612, 0.5683360504867085, 0.7117066045710376, 0.3306013222697761, 0.740246314703937, 0.55460008620593, 0.7698172018834595, 0.5505657709572485, 1, 0.7683048126655765, 0.8591967114858423, 0.6193777489238016, 0.748653688688626, 0.797729594241299, 0.6071548290936495, 0.692808012048426, 0.4458125781044925, 0.6474397574388526, 0.4762465255604472, 0.6367525007281234, 0.8311882888327301, 0.8714088781673236, 0.6602625801789002, 0.7938060303428454, 0.6620892647104706, 0.4734984976744048, 0.8006514899636238, 0.2537130441019042, 0.42287853210637844, 0.611554399015825, 0.3849870488054934, 0.46017653945410564], [0.7297108343082662, 0.7690096246532163, 0.3288948923991439, 0.6501587878256362, 0.753066492811592, 0.7435551335247068, 0.7372570311059526, 0.7875245572215656, 0.664467487114323, 0.3422443617510079, 0.2611617325429528, 0.7669780297686747, 0.661813879440015, 0.7457461358676601, 0.48566103972584646, 0.6696918816619033, 0.773162295591577, 0.18409189627364958, 0.517710330082575, 0.6183729229882693, 0.40307545992438654, 0.6306250032070329, 0.5099357130964174, 0.6968950205199295, 0.5135019248777569, 0.7683048126655765, 1, 0.777326628543929, 0.55857026126385, 0.6713204098826157, 0.7072424635013722, 0.5193356996577919, 0.6194272618581707, 0.4752319190934696, 0.5641062863546763, 0.43187142547854684, 0.5743658276385634, 0.7306947089808198, 0.7580234380792493, 0.6075414176075865, 0.703113375204891, 0.5903731443415158, 0.41905442211552785, 0.7347711499391655, 0.2107988777863255, 0.3197973637109561, 0.5451813971350361, 0.32078694875427266, 0.39028068710803765], [0.865965881210877, 0.8930033037058612, 0.365455819720716, 0.7478745400722095, 0.8643867108608456, 0.8866385097800491, 0.8681099957875211, 0.8812231581356895, 0.7811867441222988, 0.3605333020980218, 0.27060015151595235, 0.8970353719874615, 0.7417114407692309, 0.8733012229238581, 0.5566987138670147, 0.7489843498535167, 0.9024162317782997, 0.22408153104444425, 0.5991690590309057, 0.7031635861677678, 0.4530878567785834, 0.7372021088661385, 0.6092566481330048, 0.7759102095008025, 0.5984174151810282, 0.8591967114858423, 0.777326628543929, 1, 0.6520641769031137, 0.784579441950222, 0.8044686424016327, 0.5839299355915881, 0.6990938451466762, 0.5372576294054472, 0.6194841861559464, 0.4745798635012388, 0.680042534558705, 0.8299703686763854, 0.8383738877635843, 0.6545265461634948, 0.7785697215690676, 0.6607124746711817, 0.46184943040425547, 0.8049981743768706, 0.21912706344099486, 0.4232906251848606, 0.6940530750728878, 0.35082245195100187, 0.4424388265850058], [0.5829790415363104, 0.635408354759048, 0.2589888013303253, 0.5356986247986407, 0.6024015484541502, 0.6080502432042149, 0.6188583481051368, 0.6225380661876514, 0.5897115680362807, 0.25437654913238533, 0.17858224336483322, 0.62504084169176, 0.5572831738639601, 0.6315751681804079, 0.4079221366530104, 0.5062611497760164, 0.6181574699432231, 0.16022406306191422, 0.4754848039878175, 0.46266573482408696, 0.2364061117243543, 0.5249135380829218, 0.4089474842759252, 0.5486135955910426, 0.4649757690716436, 0.6193777489238016, 0.55857026126385, 0.6520641769031137, 1, 0.5421647306215389, 0.5937493104341471, 0.4082308311238616, 0.4674924528135509, 0.353013693350442, 0.4938332619680478, 0.34985566635073123, 0.5184147638229449, 0.5825903846719864, 0.5872331363643086, 0.48610342687389635, 0.524036774962147, 0.4465061325054942, 0.3342669993347543, 0.6105659165463133, 0.16767068769722784, 0.27612233790849494, 0.5015095430851052, 0.2720560941117553, 0.31485123050792624], [0.7576893613343423, 0.797492750862137, 0.332561793120634, 0.7023789202059109, 0.7924211032148927, 0.8313996370844094, 0.7490532091300146, 0.7734690685488457, 0.7023801122326925, 0.3290693678600206, 0.2307726931673192, 0.7647310504647976, 0.6467140918636151, 0.7453497280160792, 0.46981405435516094, 0.6744183214430425, 0.8103423065296728, 0.20949273926649445, 0.5096501215604857, 0.6032967514065071, 0.35815369539393493, 0.5815793468551865, 0.5687977106974668, 0.6460871119366836, 0.5292375159885326, 0.748653688688626, 0.6713204098826157, 0.784579441950222, 0.5421647306215389, 1, 0.6998866002852934, 0.4989776591780582, 0.5869157351856477, 0.5048319323165165, 0.5466042653120797, 0.3860324735441583, 0.6371551108171288, 0.7165037117416048, 0.7188523567210047, 0.5476115125692331, 0.6979915440698208, 0.5688748915354298, 0.3866308938279699, 0.7182117457499955, 0.15253605838879286, 0.3619428564910554, 0.6592789115274021, 0.31348797803424006, 0.38546646525644646], [0.8104932903685567, 0.7997242457898974, 0.3256271004277091, 0.6644295171444999, 0.7812130356223989, 0.8010398316073291, 0.7914241621671899, 0.8274812489399561, 0.6955346205246886, 0.2826658376490538, 0.2550578695507802, 0.8247053920287583, 0.707613138963918, 0.7862720297870102, 0.5294858668698871, 0.6861057554031478, 0.8181902055079902, 0.2202084589850863, 0.5485905233571204, 0.6457006464767121, 0.38956068908077074, 0.6769144268035497, 0.5215755355083198, 0.7303841685699854, 0.5533921956646682, 0.797729594241299, 0.7072424635013722, 0.8044686424016327, 0.5937493104341471, 0.6998866002852934, 1, 0.5478671257247625, 0.6350082882849225, 0.4612150454361289, 0.6173462154123617, 0.44122373413687926, 0.6488664077889144, 0.7707188138250279, 0.7683106043167516, 0.6488256523396317, 0.7570589919147112, 0.5952550384641359, 0.4246170610704334, 0.7827853275409428, 0.2335875614747558, 0.37449858177102446, 0.615845475633636, 0.34478560637060685, 0.4125041136884854], [0.5924104356767549, 0.5626139087663719, 0.2670143947666841, 0.4822869632219892, 0.5559549199646076, 0.5568242945686606, 0.5650849926206424, 0.5975493831201029, 0.49800251168245696, 0.22683366384961168, 0.19174862376788773, 0.5811810118735568, 0.5230057763317011, 0.5750037646106311, 0.3576100406413816, 0.5388372689574177, 0.5837370585124132, 0.202711436373638, 0.425927647599659, 0.44498319759933874, 0.2437641223258339, 0.5119838508669989, 0.38186458562150905, 0.5708138989284883, 0.36742888264480683, 0.6071548290936495, 0.5193356996577919, 0.5839299355915881, 0.4082308311238616, 0.4989776591780582, 0.5478671257247625, 1, 0.4650447424635075, 0.2759288463890296, 0.4485655887916964, 0.31419584507118026, 0.4817269710595477, 0.5627373020022971, 0.5546743042090616, 0.420036533692723, 0.5372357022135199, 0.44159515346689626, 0.3372848920989673, 0.5542510180302305, 0.21160358849501296, 0.3699165650589766, 0.4183395444057213, 0.27401746272071026, 0.2919339625211619], [0.6816160142249588, 0.6776513314151874, 0.28833004125496925, 0.5877635433407234, 0.6623569945510271, 0.6599513344723986, 0.6760441811594454, 0.7131147812464499, 0.5884464022700008, 0.28924962794204456, 0.22097542997372682, 0.7275115576480358, 0.5874635220315619, 0.7039791835687518, 0.4913959890791104, 0.5924475124439436, 0.6954999452070106, 0.17850956156743816, 0.425097407795924, 0.5606958648398861, 0.39116334984354867, 0.618937386264665, 0.4099749649172086, 0.6504533911403361, 0.436068375159094, 0.692808012048426, 0.6194272618581707, 0.6990938451466762, 0.4674924528135509, 0.5869157351856477, 0.6350082882849225, 0.4650447424635075, 1, 0.41660620736390674, 0.521278259079332, 0.38674524907806035, 0.5233467282971463, 0.6817454615199218, 0.7009692330645826, 0.49660724404305084, 0.6302662863937356, 0.5666760435616514, 0.3632574777706929, 0.627581519003848, 0.2294483495378369, 0.31239208168209603, 0.4961399388925093, 0.31156703624543597, 0.3991709857132136], [0.4556037931990281, 0.527706047588997, 0.20144410134945678, 0.5243022182523336, 0.5071839269412645, 0.5213366072975633, 0.44007483722961666, 0.4890450433133643, 0.44864064901229794, 0.20412050010021401, 0.1662286958048068, 0.49219904348282245, 0.4232079430798325, 0.46544747858108293, 0.32492001738207893, 0.47060965370130486, 0.5570227623133962, 0.10887215907883849, 0.36910894731982286, 0.40237339219216384, 0.45082104754473573, 0.35845977098072734, 0.38219697394464347, 0.43995022903582537, 0.39148537430689007, 0.4458125781044925, 0.4752319190934696, 0.5372576294054472, 0.353013693350442, 0.5048319323165165, 0.4612150454361289, 0.2759288463890296, 0.41660620736390674, 1, 0.3034483747388973, 0.254457848234766, 0.38640177788325714, 0.474941047866334, 0.4814548827650196, 0.3474753221601167, 0.4197137155895487, 0.3895977889650271, 0.23649638970573977, 0.4690197490974467, 0.15313436520881515, -0.020520467130400056, 0.4118900578737575, 0.21110997322135425, 0.27941006931115125], [0.6518783345022898, 0.6237232896883227, 0.2578670783696155, 0.5188432058986974, 0.6147280864835156, 0.6241810076893011, 0.6589491147119237, 0.6509070364702314, 0.5603400581056495, 0.26694833992069644, 0.1842493734925467, 0.6536137385284132, 0.5521370717684706, 0.6283903644832871, 0.40316128021244596, 0.5743393625526828, 0.6269900759298679, 0.20738866094430403, 0.43062746921102163, 0.47125162581303653, 0.2089182059148753, 0.5668969312120851, 0.44513576956941897, 0.5845794669042784, 0.4361381245187454, 0.6474397574388526, 0.5641062863546763, 0.6194841861559464, 0.4938332619680478, 0.5466042653120797, 0.6173462154123617, 0.4485655887916964, 0.521278259079332, 0.3034483747388973, 1, 0.3728199781672576, 0.5091051336895551, 0.6007084282216341, 0.6090709073069496, 0.5517801694125386, 0.6052008158200142, 0.4783715446692055, 0.3976330597381685, 0.6214108941226603, 0.19685078472753254, 0.3268867310898156, 0.4744643701321346, 0.27927411468817276, 0.3303257273694925], [0.46371351398482796, 0.45520775498570654, 0.21270779883264945, 0.3595915534615474, 0.45034230412284454, 0.44962275895111947, 0.478137420471331, 0.47426150848685483, 0.4237054846919552, 0.20457454747958523, 0.17999861919065466, 0.47912032302484037, 0.39448025568567086, 0.48409934369954033, 0.3039403131600632, 0.42806173126495795, 0.4740069596656412, 0.11099243637855767, 0.32005572359187967, 0.3898708420311344, 0.26399569678983803, 0.4215365095421521, 0.33510414733706567, 0.43533142844165995, 0.31085030289017535, 0.4762465255604472, 0.43187142547854684, 0.4745798635012388, 0.34985566635073123, 0.3860324735441583, 0.44122373413687926, 0.31419584507118026, 0.38674524907806035, 0.254457848234766, 0.3728199781672576, 1, 0.3630883051142833, 0.45681570945875627, 0.46636127851954734, 0.38696436899308084, 0.4231412131665112, 0.38468278008729834, 0.23915209640671842, 0.42715553883995444, 0.14772611815403996, 0.17988037661233094, 0.3449233638084092, 0.1673390475991101, 0.3090648113169451], [0.6576962805803057, 0.6786422695133907, 0.29776376063893745, 0.5735867348627027, 0.6451528004857084, 0.6974478687317146, 0.6538722758986445, 0.6576927496521051, 0.5890277721714995, 0.23753595412171713, 0.18446660561227274, 0.672170427518673, 0.5921106891703877, 0.6436993897363721, 0.44686230225299983, 0.5783646125463775, 0.6945604245680249, 0.17369007266940464, 0.48997895719032897, 0.5321172950897487, 0.3300730611918849, 0.551088045977826, 0.5023023684488164, 0.6205428017590721, 0.5364000044895807, 0.6367525007281234, 0.5743658276385634, 0.680042534558705, 0.5184147638229449, 0.6371551108171288, 0.6488664077889144, 0.4817269710595477, 0.5233467282971463, 0.38640177788325714, 0.5091051336895551, 0.3630883051142833, 1, 0.6358965036660601, 0.6283249094247612, 0.5260905332689245, 0.6232844051893683, 0.5027660094164504, 0.3779454314276748, 0.6572999378075303, 0.19889474341320593, 0.38095790683762953, 0.6062959192441979, 0.28694692373945263, 0.3323226805532918], [0.8150954307689653, 0.8186911086404077, 0.3649511103116572, 0.6977724211430392, 0.80131454200536, 0.8076732342577193, 0.8264950090991093, 0.8562977219607063, 0.7220631620922536, 0.3506740698827862, 0.24167793865974685, 0.8547364483935744, 0.7018961685907997, 0.8249613658558704, 0.5626745826565899, 0.7572175699351008, 0.833108513880794, 0.20171731832289078, 0.5751060006833564, 0.6999830193350762, 0.3970253256964555, 0.70997273298016, 0.5472886864946094, 0.7325925266916579, 0.5439427673715354, 0.8311882888327301, 0.7306947089808198, 0.8299703686763854, 0.5825903846719864, 0.7165037117416048, 0.7707188138250279, 0.5627373020022971, 0.6817454615199218, 0.474941047866334, 0.6007084282216341, 0.45681570945875627, 0.6358965036660601, 1, 0.8060004973704433, 0.6467762167996991, 0.7728259989970635, 0.6379343141612753, 0.43316819691615543, 0.7687129299374074, 0.23132354912601866, 0.3606265887504831, 0.5945102905856576, 0.36026534758358036, 0.4222487623931133], [0.8109869655801457, 0.827903969070813, 0.3586410363568399, 0.694540972022311, 0.8238848040997583, 0.8110420584836157, 0.8100665012361495, 0.8780202335409746, 0.7295900381814762, 0.3526040997249608, 0.4875636665448887, 0.8663498839140469, 0.6960845288191622, 0.8292104240662349, 0.5389000162059105, 0.7375174386159399, 0.8484487731746689, 0.20216456107642622, 0.5550726456867593, 0.6717199919346304, 0.42734319607512067, 0.7275551545841104, 0.5389185753911498, 0.7571666638947978, 0.5359603116439305, 0.8714088781673236, 0.7580234380792493, 0.8383738877635843, 0.5872331363643086, 0.7188523567210047, 0.7683106043167516, 0.5546743042090616, 0.7009692330645826, 0.4814548827650196, 0.6090709073069496, 0.46636127851954734, 0.6283249094247612, 0.8060004973704433, 1, 0.6563307169095143, 0.768185273873342, 0.652010548183845, 0.41466936676330207, 0.7843423235977527, 0.2090831026865386, 0.4357934163717844, 0.5986783119242406, 0.36185988303543143, 0.424592214247768], [0.6846377266429023, 0.6618561796713425, 0.2715249783660158, 0.5155595682961401, 0.6428493250516379, 0.6536710843482162, 0.7121517240572611, 0.693068488431548, 0.6230991049087516, 0.2858241859256449, 0.2568621395874813, 0.6995993981020716, 0.6052258484998115, 0.6811006292997583, 0.4857250228725273, 0.5798297155271349, 0.656008154267582, 0.17433976780740243, 0.4244993270400992, 0.5447789050377078, 0.29133258872205753, 0.5807974280720085, 0.4463165738230815, 0.5950059328105081, 0.4465075655278563, 0.6602625801789002, 0.6075414176075865, 0.6545265461634948, 0.48610342687389635, 0.5476115125692331, 0.6488256523396317, 0.420036533692723, 0.49660724404305084, 0.3474753221601167, 0.5517801694125386, 0.38696436899308084, 0.5260905332689245, 0.6467762167996991, 0.6563307169095143, 1, 0.6526219321371455, 0.4772800105791711, 0.4095095185422073, 0.699509496593549, 0.13611398992746784, 0.21425905758689268, 0.5173838635505044, 0.3305719332979499, 0.35131753328790954], [0.7969811315720045, 0.7809173897573533, 0.30123441966484593, 0.6507457617293304, 0.7684366225693503, 0.770174381123015, 0.7804761716147748, 0.8340465456099113, 0.6929913023855454, 0.28083864064049735, 0.2482137248945712, 0.8193645689431447, 0.6603792331998247, 0.7889779950354513, 0.5150888151321936, 0.6848034340396245, 0.799559063000362, 0.19661614589730428, 0.5296933550779928, 0.6107361601169621, 0.3859657593247586, 0.6894576672319052, 0.5046502034846476, 0.7212411061795921, 0.5277503902925067, 0.7938060303428454, 0.703113375204891, 0.7785697215690676, 0.524036774962147, 0.6979915440698208, 0.7570589919147112, 0.5372357022135199, 0.6302662863937356, 0.4197137155895487, 0.6052008158200142, 0.4231412131665112, 0.6232844051893683, 0.7728259989970635, 0.768185273873342, 0.6526219321371455, 1, 0.566992309764183, 0.4451548602444686, 0.7594069172527085, 0.20794534995332672, 0.3462861710669127, 0.5584417326735021, 0.31470119788269135, 0.43339705700272385], [0.6164524272997818, 0.6345719488279706, 0.2966761836518211, 0.5331247331709942, 0.6240312651962905, 0.6294991523793647, 0.6212680419735105, 0.6635101052730389, 0.5556188155155783, 0.3046580608609465, 0.23512718998229787, 0.6663789932573703, 0.5714560490214057, 0.6334889266278599, 0.4355431183360689, 0.5615089730180831, 0.6681651593313045, 0.16074902358662746, 0.4384385563581909, 0.5327869493482574, 0.3735377857889201, 0.560620008982558, 0.4152454888614595, 0.5734526314972103, 0.4162161189835776, 0.6620892647104706, 0.5903731443415158, 0.6607124746711817, 0.4465061325054942, 0.5688748915354298, 0.5952550384641359, 0.44159515346689626, 0.5666760435616514, 0.3895977889650271, 0.4783715446692055, 0.38468278008729834, 0.5027660094164504, 0.6379343141612753, 0.652010548183845, 0.4772800105791711, 0.566992309764183, 1, 0.3765714835949293, 0.5961364648842759, 0.1691168669774406, 0.32002845246970607, 0.45680029490989876, 0.2649685813484953, 0.35869581237687226], [0.4670753099888584, 0.49251824381711545, 0.1582661861154594, 0.3927734330511016, 0.5002243575995712, 0.4675453654304379, 0.46373574540640156, 0.4759491482096237, 0.40443394633419943, 0.23897228240210772, 0.14086358371805355, 0.4779969208854028, 0.43404994711800343, 0.4501315325436523, 0.2864779694383888, 0.38680320307617355, 0.4836813186963104, 0.1633412846930407, 0.35910088165154774, 0.3525447777165428, 0.2055386293001575, 0.36818704563357446, 0.3251982325680836, 0.3994701550698036, 0.33240361786336065, 0.4734984976744048, 0.41905442211552785, 0.46184943040425547, 0.3342669993347543, 0.3866308938279699, 0.4246170610704334, 0.3372848920989673, 0.3632574777706929, 0.23649638970573977, 0.3976330597381685, 0.23915209640671842, 0.3779454314276748, 0.43316819691615543, 0.41466936676330207, 0.4095095185422073, 0.4451548602444686, 0.3765714835949293, 1, 0.46554429111330947, 0.1398002935066751, 0.4359504988866661, 0.35455844774134015, 0.17214037905222052, 0.22816379343990664], [0.7966439025869176, 0.8160383702338717, 0.3257589607475293, 0.6822812496517481, 0.7823880680776243, 0.8136502626044819, 0.8214184131522386, 0.8258914117471309, 0.7370779927994218, 0.34089587108913627, 0.251563266856023, 0.8229143111875248, 0.7440230454067485, 0.7980935366135766, 0.5225147935222171, 0.6990380361048294, 0.8218247818272447, 0.22564522532888565, 0.5375892886137237, 0.6389086322279299, 0.40167485041962125, 0.654492830044202, 0.5338917659738438, 0.7177491740747094, 0.594228100863131, 0.8006514899636238, 0.7347711499391655, 0.8049981743768706, 0.6105659165463133, 0.7182117457499955, 0.7827853275409428, 0.5542510180302305, 0.627581519003848, 0.4690197490974467, 0.6214108941226603, 0.42715553883995444, 0.6572999378075303, 0.7687129299374074, 0.7843423235977527, 0.699509496593549, 0.7594069172527085, 0.5961364648842759, 0.46554429111330947, 1, 0.2102359486058894, 0.4241696046664526, 0.6202890365119319, 0.33151321420913904, 0.398755440009659], [0.23451515621052663, 0.21717993600790655, 0.11199141087285773, 0.19337954931554058, 0.23103563682280512, 0.22314689389548853, 0.22453103657939336, 0.2130012738893111, 0.2001477476738158, 0.11067086413161949, -0.03651107994640663, 0.2430755452210987, 0.22755400335266654, 0.24833669644856074, 0.1612015780320931, 0.2246681035142802, 0.24345276929175338, 0.10828153052069799, 0.21549876563406958, 0.15182035728285984, 0.0799760309823122, 0.20816739990921612, 0.12290970835246504, 0.22922059956141888, 0.17792083178837373, 0.2537130441019042, 0.2107988777863255, 0.21912706344099486, 0.16767068769722784, 0.15253605838879286, 0.2335875614747558, 0.21160358849501296, 0.2294483495378369, 0.15313436520881515, 0.19685078472753254, 0.14772611815403996, 0.19889474341320593, 0.23132354912601866, 0.2090831026865386, 0.13611398992746784, 0.20794534995332672, 0.1691168669774406, 0.1398002935066751, 0.2102359486058894, 1, 0.35761116015172795, 0.14119472561426394, 0.152112696516202, 0.17259231839083714], [0.4130030646450816, 0.40626038923564417, 0.32127863798559225, 0.39241797222610564, 0.41187406673705046, 0.4390030094456399, 0.4010595866534586, 0.39498542006052484, 0.43680893522446124, 0.24384504629252815, 0.03044538214810526, 0.39762502869543126, 0.3277274477067024, 0.3772528257642741, 0.22316331318324528, 0.4012923258022414, 0.40001083087961803, 0.33062721514772686, 0.3671075234426417, 0.37159728306170514, -0.24928183227492326, 0.2776981605532258, 0.3862789728644773, 0.3281897460618858, 0.21370678426981402, 0.42287853210637844, 0.3197973637109561, 0.4232906251848606, 0.27612233790849494, 0.3619428564910554, 0.37449858177102446, 0.3699165650589766, 0.31239208168209603, -0.020520467130400056, 0.3268867310898156, 0.17988037661233094, 0.38095790683762953, 0.3606265887504831, 0.4357934163717844, 0.21425905758689268, 0.3462861710669127, 0.32002845246970607, 0.4359504988866661, 0.4241696046664526, 0.35761116015172795, 1, 0.3673406760945009, 0.180071855267483, 0.10869308230300757], [0.6469357170692362, 0.689805265092909, 0.27918559621778355, 0.5382972744427756, 0.6346171468542742, 0.725614666792474, 0.6723918991673666, 0.6298922731758342, 0.6672511838723032, 0.2740136955126952, 0.16625955869782147, 0.6466940692464547, 0.571495425557669, 0.6069535300195092, 0.4029385227667962, 0.5566747565996849, 0.6858684069406581, 0.16087105202912097, 0.4299311085473435, 0.508380828926691, 0.319064235843815, 0.4936382752948575, 0.5369470367483534, 0.5466725400492003, 0.49975100670005396, 0.611554399015825, 0.5451813971350361, 0.6940530750728878, 0.5015095430851052, 0.6592789115274021, 0.615845475633636, 0.4183395444057213, 0.4961399388925093, 0.4118900578737575, 0.4744643701321346, 0.3449233638084092, 0.6062959192441979, 0.5945102905856576, 0.5986783119242406, 0.5173838635505044, 0.5584417326735021, 0.45680029490989876, 0.35455844774134015, 0.6202890365119319, 0.14119472561426394, 0.3673406760945009, 1, 0.3265611341444412, 0.3126212289875302], [0.35844487920597207, 0.34424009451656745, 0.12391380561803007, 0.2596932426072543, 0.3339229303899729, 0.347775525492682, 0.3701528763373425, 0.36626712383654, 0.3302107758724547, 0.1664348377989984, 0.09900651909531864, 0.36843983767805766, 0.33248842306752613, 0.3715895516108034, 0.21713823705072025, 0.32085815613585034, 0.362087644267486, 0.055940453092359066, 0.2538188771425222, 0.3008135779553687, 0.13964352435370692, 0.3160528846940563, 0.22261316133975165, 0.30079236553522803, 0.19522701104460569, 0.3849870488054934, 0.32078694875427266, 0.35082245195100187, 0.2720560941117553, 0.31348797803424006, 0.34478560637060685, 0.27401746272071026, 0.31156703624543597, 0.21110997322135425, 0.27927411468817276, 0.1673390475991101, 0.28694692373945263, 0.36026534758358036, 0.36185988303543143, 0.3305719332979499, 0.31470119788269135, 0.2649685813484953, 0.17214037905222052, 0.33151321420913904, 0.152112696516202, 0.180071855267483, 0.3265611341444412, 1, 0.17940603909806083], [0.4056244316315685, 0.425408780407193, 0.18171325412820918, 0.3880110797731558, 0.4419887233572881, 0.4300509108158493, 0.419952318707894, 0.47388974398249856, 0.3967707723979441, 0.20255763646023311, 0.108907576618161, 0.44497799905571295, 0.39548087031948936, 0.4577960410256718, 0.3134836037557546, 0.3584950444078775, 0.4502775292129621, 0.06554241260394056, 0.31157768573560224, 0.38532953064119274, 0.2519193624214589, 0.3744953651159128, 0.29953053030770926, 0.41304592751685315, 0.27134790511253315, 0.46017653945410564, 0.39028068710803765, 0.4424388265850058, 0.31485123050792624, 0.38546646525644646, 0.4125041136884854, 0.2919339625211619, 0.3991709857132136, 0.27941006931115125, 0.3303257273694925, 0.3090648113169451, 0.3323226805532918, 0.4222487623931133, 0.424592214247768, 0.35131753328790954, 0.43339705700272385, 0.35869581237687226, 0.22816379343990664, 0.398755440009659, 0.17259231839083714, 0.10869308230300757, 0.3126212289875302, 0.17940603909806083, 1]], "timestamp": 1749829452693, "timeframe": "1h", "period": 30}}, "probabilities": {"predictions": [{"symbol": "1000PEPEUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454250}, {"symbol": "DOGSUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454280}, {"symbol": "HMSTRUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3209960667867327, "expectedReturn": 0.0018087633186100304, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454310}, {"symbol": "10000SATSUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3829339246573617, "expectedReturn": 0.002499980432206099, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454347}, {"symbol": "NOTUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3435767903936492, "expectedReturn": 0.0024051504914474543, "confidence": 0.8800000000000001, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454392}, {"symbol": "BOMEUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3674958508216675, "expectedReturn": 0.0029614762489586985, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454450}, {"symbol": "1000BONKUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454481}, {"symbol": "GALAUSDT", "timeframe": 1, "direction": "UP", "probability": 0.30063692003723935, "expectedReturn": 0.0021102117339674886, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454508}, {"symbol": "MEWUSDT", "timeframe": 1, "direction": "UP", "probability": 0.37091921969398567, "expectedReturn": 0.0026060558287281125, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454538}, {"symbol": "ANIMEUSDT", "timeframe": 1, "direction": "UP", "probability": 0.41030166566403464, "expectedReturn": 0.003633323394584679, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation", "low_volume"], "timestamp": 1749829454564}, {"symbol": "RVNUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454590}, {"symbol": "SHIB1000USDT", "timeframe": 1, "direction": "UP", "probability": 0.27099906242636596, "expectedReturn": 0.0014720675489378649, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454617}, {"symbol": "PENGUUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454644}, {"symbol": "DOGEUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454670}, {"symbol": "MOBILEUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bearish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454697}, {"symbol": "XVGUSDT", "timeframe": 1, "direction": "UP", "probability": 0.37115929880989335, "expectedReturn": 0.0025901271747354937, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829454725}, {"symbol": "MEMEUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454753}, {"symbol": "ZBCNUSDT", "timeframe": 1, "direction": "UP", "probability": 0.29593237395328603, "expectedReturn": 0.006233251392990052, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454784}, {"symbol": "10000WHYUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3683429954405359, "expectedReturn": 0.0020092865044923193, "confidence": 0.8800000000000001, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "High volume confirmation", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["weak_correlation"], "timestamp": 1749829454865}, {"symbol": "SPELLUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829454975}, {"symbol": "ORBSUSDT", "timeframe": 1, "direction": "DOWN", "probability": 0.3202572185938443, "expectedReturn": -0.001159800956370272, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bearish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "High volatility environment"], "timestamp": 1749829455123}, {"symbol": "10000ELONUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "low_volume"], "timestamp": 1749829455167}, {"symbol": "HIPPOUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455274}, {"symbol": "10000LADYSUSDT", "timeframe": 1, "direction": "UP", "probability": 0.4984685779938062, "expectedReturn": 0.0021956443257817853, "confidence": 1, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455313}, {"symbol": "ZEREBROUSDT", "timeframe": 1, "direction": "DOWN", "probability": 0.3292492556720043, "expectedReturn": -0.003289362945653193, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "High volatility environment", "weak_correlation", "low_volume"], "timestamp": 1749829455337}, {"symbol": "DENTUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3824593618121508, "expectedReturn": 0.001803822374381019, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455360}, {"symbol": "LEVERUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3071843357649711, "expectedReturn": 0.0019774750016729745, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455383}, {"symbol": "1000000BABYDOGEUSDT", "timeframe": 1, "direction": "UP", "probability": 0.27727188690451937, "expectedReturn": 0.0020405729673284817, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455406}, {"symbol": "DOGUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455433}, {"symbol": "PEOPLEUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455464}, {"symbol": "BRETTUSDT", "timeframe": 1, "direction": "UP", "probability": 0.43722952212293814, "expectedReturn": 0.0035377869367017815, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "High volume confirmation", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": [], "timestamp": 1749829455514}, {"symbol": "B3USDT", "timeframe": 1, "direction": "UP", "probability": 0.2641327628083418, "expectedReturn": 0.0025334265030409905, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455559}, {"symbol": "1000BTTUSDT", "timeframe": 1, "direction": "DOWN", "probability": 0.3719814258285681, "expectedReturn": -0.0009103349656432168, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "low_volume"], "timestamp": 1749829455593}, {"symbol": "1000RATSUSDT", "timeframe": 1, "direction": "UP", "probability": 0.30896638108816865, "expectedReturn": 0.0027851687063473914, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "High volatility environment", "weak_correlation"], "timestamp": 1749829455626}, {"symbol": "GIGAUSDT", "timeframe": 1, "direction": "UP", "probability": 0.4381442820153379, "expectedReturn": 0.003764588876510427, "confidence": 0.8800000000000001, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455652}, {"symbol": "MYRIAUSDT", "timeframe": 1, "direction": "UP", "probability": 0.3360197504173234, "expectedReturn": 0.0037510609329633677, "confidence": 0.66, "supportingFactors": ["MACD bearish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455679}, {"symbol": "DEGENUSDT", "timeframe": 1, "direction": "UP", "probability": 0.24995140142957656, "expectedReturn": 0.0032446792476359967, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455704}, {"symbol": "JASMYUSDT", "timeframe": 1, "direction": "UP", "probability": 0.27096571220272614, "expectedReturn": 0.0018250240352996451, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation"], "timestamp": 1749829455772}, {"symbol": "HOTUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455846}, {"symbol": "FARTCOINUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal", "High volatility environment"], "timestamp": 1749829455874}, {"symbol": "ENAUSDT", "timeframe": 1, "direction": "UP", "probability": 0.28277827536676514, "expectedReturn": 0.0028135778102455914, "confidence": 0.66, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455910}, {"symbol": "IOSTUSDT", "timeframe": 1, "direction": "UP", "probability": 0.2999921458257048, "expectedReturn": 0.0017989804470799928, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455937}, {"symbol": "GORKUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455962}, {"symbol": "AI16ZUSDT", "timeframe": 1, "direction": "UP", "probability": 0.39097165768249087, "expectedReturn": 0.003674244244359989, "confidence": 0.8800000000000001, "supportingFactors": ["RSI overbought - potential pullback", "MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829455985}, {"symbol": "OBTUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829456010}, {"symbol": "SKATEUSDT", "timeframe": 1, "direction": "UP", "probability": 0.2829483314732604, "expectedReturn": 0.0035765509336697786, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "51 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "High volatility environment", "weak_correlation"], "timestamp": 1749829456010}, {"symbol": "MOODENGUSDT", "timeframe": 1, "direction": "NEUTRAL", "probability": 0.5, "expectedReturn": -0.0032659863237109042, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal"], "timestamp": 1749829456034}, {"symbol": "XEMUSDT", "timeframe": 1, "direction": "UP", "probability": 0.26486039245836424, "expectedReturn": 0.0029508694473369852, "confidence": 0.66, "supportingFactors": ["MACD bullish momentum", "669 similar historical patterns", "correlation_analysis"], "riskFactors": ["Low volume - weak signal", "weak_correlation", "low_volume"], "timestamp": 1749829456066}, {"symbol": "1000CATSUSDT", "timeframe": 1, "direction": "UP", "probability": 0.41444369224870803, "expectedReturn": 0.0027930182126973168, "confidence": 0.8800000000000001, "supportingFactors": ["RSI oversold - potential bounce", "MACD bearish momentum", "Price below <PERSON><PERSON><PERSON> lower band", "High volume confirmation", "669 similar historical patterns", "correlation_analysis", "high_volume"], "riskFactors": ["High volatility environment"], "timestamp": 1749829456090}], "timestamp": 1749829456090}}