export const bybit = {
    apiKey: 'JTryt12r8658vKUEAu',
    apiSecret: '0OAvgt0qyTYYvq89ESgAyHxGp213dYZPaF0D',
    demo: true, // this its only for the bybit api client
    leverage: 2,
}

// Centralized Trading Configuration
export const tradingConfig = {
    // Core Trading Parameters
    leverage: 2, // Fixed 2x leverage for all positions
    timeframe: "60", // 1 hour timeframe for analysis
    timeframeMinutes: 60, // 1 hour in minutes

    // Rebalancing Configuration
    rebalanceInterval: 60 * 60 * 1000, // 1 hour in milliseconds
    analysisInterval: 60 * 60 * 1000, // 1 hour analysis refresh

    // Portfolio Management
    maxPositions: 20, // Maximum total positions (10 long + 10 short)
    maxPositionsPerSide: 10, // Maximum positions per side for market neutrality
    riskPerPosition: 0.05, // 5% risk per position
    minPositionSize: 10, // Minimum $10 position size
    marketNeutralThreshold: 0.1, // 10% deviation tolerance for neutrality
    portfolioUtilization: 0.8, // Use 80% of available balance

    // Data Management
    topSymbolsCount: 50, // Number of top symbols to analyze
    daysBack: 30, // Historical data lookback period
    cacheExpiryHours: 1, // Cache expiry time in hours
    maxCandlesPerRequest: 1000, // API request limit

    // Risk Management
    maxLeverage: 3, // Maximum allowed leverage
    maxCorrelation: 0.7, // Maximum correlation between positions
    minConfidenceThreshold: 0.6, // Minimum confidence for signals
    minSignalStrength: 0.5, // Minimum signal strength

    // Analysis Parameters
    performanceWindow: 30, // Days for performance analysis
    predictionTimeframes: [1, 4, 24], // Hours for price predictions

    // Rate Limiting
    requestsPerSecond: 15,
    requestsPerMinute: 120,
    burstLimit: 8,
    useAPIRateLimit: true
}